/* Grades Page Specific Styles */

/* Grades Container */
.grades-container {
    padding: 8px 16px 80px;
}

/* Grade Card */
.grade-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: var(--elevation-1);
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.grade-card:hover {
    box-shadow: var(--elevation-2);
    transform: translateY(-1px);
}

.grade-card:active {
    transform: translateY(0);
    box-shadow: var(--elevation-1);
}

/* Grade Media */
.grade-media {
    position: relative;
    flex-shrink: 0;
}

.grade-badge {
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--surface-color);
    font-size: 12px;
    font-weight: 700;
    color: var(--text-light);
}

.grade-badge.grade-a {
    background-color: #4caf50; /* Green for A */
}

.grade-badge.grade-b {
    background-color: #2196f3; /* Blue for B */
}

.grade-badge.grade-c {
    background-color: #ff9800; /* Orange for C */
}

.grade-badge.grade-d {
    background-color: #f44336; /* Red for D */
}

.grade-badge.grade-f {
    background-color: #9e9e9e; /* Gray for F */
}

/* Grade Content */
.grade-content {
    flex: 1;
    min-width: 0;
}

.grade-info {
    margin-bottom: 8px;
}

.course-name {
    font-size: 14px;
    color: var(--primary-color);
    margin: 0 0 2px;
    font-weight: 500;
}

.assignment-name {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

.grade-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.grade-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.grade-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.grade-score {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.grade-date {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Grade Statistics Cards */
.grade-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    padding: 16px;
    background-color: var(--surface-color);
    margin-bottom: 8px;
}

.stat-card {
    text-align: center;
    padding: 16px 12px;
    border-radius: 8px;
    background-color: var(--background-color);
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 4px 0 0;
}

.stat-card.excellent .stat-number {
    color: #4caf50;
}

.stat-card.good .stat-number {
    color: #2196f3;
}

.stat-card.average .stat-number {
    color: #ff9800;
}

.stat-card.poor .stat-number {
    color: #f44336;
}

/* Grade Distribution Chart */
.grade-distribution {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 16px;
    margin: 16px;
    box-shadow: var(--elevation-1);
}

.distribution-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 16px;
    color: var(--text-primary);
}

.distribution-bar {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.distribution-label {
    width: 40px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.distribution-progress {
    flex: 1;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    margin: 0 12px;
    overflow: hidden;
}

.distribution-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.distribution-fill.grade-a {
    background-color: #4caf50;
}

.distribution-fill.grade-b {
    background-color: #2196f3;
}

.distribution-fill.grade-c {
    background-color: #ff9800;
}

.distribution-fill.grade-d {
    background-color: #f44336;
}

.distribution-fill.grade-f {
    background-color: #9e9e9e;
}

.distribution-count {
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 30px;
    text-align: right;
}

/* Subject Tabs */
.subject-tabs {
    display: flex;
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
    padding: 0 16px;
}

.subject-tab {
    background: none;
    border: none;
    padding: 16px 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.subject-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.subject-tab:hover:not(.active) {
    color: var(--text-primary);
    background-color: rgba(0, 0, 0, 0.04);
}

/* Responsive Design for Grades */
@media (max-width: 480px) {
    .grade-card {
        padding: 12px;
        gap: 12px;
    }
    
    .grade-badge {
        width: 24px;
        height: 24px;
        font-size: 10px;
        bottom: -2px;
        right: -2px;
    }
    
    .grade-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 12px;
    }
    
    .stat-card {
        padding: 12px 8px;
    }
    
    .stat-number {
        font-size: 20px;
    }
    
    .grade-distribution {
        margin: 12px;
        padding: 12px;
    }
    
    .distribution-bar {
        margin-bottom: 8px;
    }
    
    .distribution-label {
        width: 30px;
        font-size: 12px;
    }
    
    .subject-tabs {
        padding: 0 12px;
    }
    
    .subject-tab {
        padding: 12px 16px;
        font-size: 13px;
    }
}

@media (max-width: 360px) {
    .grade-stats {
        grid-template-columns: 1fr;
    }
    
    .grade-distribution {
        margin: 8px;
        padding: 8px;
    }
    
    .distribution-title {
        font-size: 14px;
    }
}
