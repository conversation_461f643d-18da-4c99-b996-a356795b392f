/* Material Design Admin Dashboard Styles */

:root {
    --primary-color: #6200ee;
    --primary-light: #9c4dff;
    --primary-dark: #3700b3;
    --secondary-color: #03dac6;
    --secondary-light: #66fff9;
    --secondary-dark: #00a896;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --error-color: #b00020;
    --text-primary: rgba(0, 0, 0, 0.87);
    --text-secondary: rgba(0, 0, 0, 0.6);
    --text-disabled: rgba(0, 0, 0, 0.38);
    --text-light: #ffffff;
    --border-color: rgba(0, 0, 0, 0.12);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --chart-blue: #4285f4;
    --chart-red: #ea4335;
    --chart-yellow: #fbbc05;
    --chart-green: #34a853;
    --chart-purple: #9c27b0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 14px;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: 240px;
    background-color: var(--surface-color);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    box-shadow: 2px 0 5px var(--shadow-color);
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.user-profile {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
}

.user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.user-name {
    font-weight: 500;
    margin: 0;
}

.user-email {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

.dropdown-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    color: var(--text-secondary);
}

.sidebar-nav {
    padding: 16px 0;
    height: calc(100vh - 73px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.nav-list {
    list-style: none;
    padding: 0;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-item.active .nav-link {
    background-color: rgba(98, 0, 238, 0.08);
    color: var(--primary-color);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s;
    border-radius: 0 24px 24px 0;
}

.nav-link:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.nav-link .material-icons {
    margin-right: 16px;
    font-size: 20px;
}

.nav-arrow {
    margin-left: auto;
    font-size: 18px;
}

.sidebar-footer {
    padding: 16px;
    color: var(--text-secondary);
    font-size: 12px;
    border-top: 1px solid var(--border-color);
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: 240px;
    transition: margin-left 0.3s ease;
}

/* Header Styles */
.header {
    height: 64px;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    padding: 0 16px;
    position: sticky;
    top: 0;
    z-index: 99;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    color: var(--text-light);
    margin-right: 16px;
    cursor: pointer;
    display: none;
}

.brand {
    font-size: 20px;
    font-weight: 500;
    margin: 0;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.search-box {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 4px 12px;
    width: 100%;
    max-width: 500px;
}

.search-icon {
    color: var(--text-light);
    margin-right: 8px;
}

.search-input {
    background: none;
    border: none;
    color: var(--text-light);
    outline: none;
    width: 100%;
    font-size: 14px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.header-right {
    display: flex;
    align-items: center;
}

.notification-bell, .chat-toggle {
    position: relative;
    margin-right: 16px;
    cursor: pointer;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--error-color);
    color: var(--text-light);
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.more-menu {
    cursor: pointer;
}

/* Dashboard Content Styles */
.dashboard-content {
    padding: 24px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.page-title {
    font-size: 20px;
    font-weight: 500;
    margin: 0;
}

.page-actions {
    display: flex;
}

.btn-text {
    background: none;
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
    padding: 8px 16px;
    transition: background-color 0.2s;
    border-radius: 4px;
}

.btn-text:hover {
    background-color: rgba(98, 0, 238, 0.08);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}

.downloads-card {
    grid-column: span 3;
}

.reports-card {
    grid-column: span 2;
}

.server-card {
    grid-column: span 1;
}

/* Card Styles */
.dashboard-card {
    background-color: var(--surface-color);
    border-radius: 4px;
    box-shadow: 0 1px 3px var(--shadow-color);
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    color: var(--text-secondary);
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon.purple {
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
}

.card-icon.blue {
    background-color: rgba(66, 133, 244, 0.1);
    color: var(--chart-blue);
}

.card-icon.yellow {
    background-color: rgba(251, 188, 5, 0.1);
    color: var(--chart-yellow);
}

.card-icon.green {
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--chart-green);
}

.card-actions {
    display: flex;
    align-items: center;
}

.card-badge {
    background-color: #f0f0f0;
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.card-body {
    padding: 16px;
}

.metric {
    font-size: 24px;
    font-weight: 500;
    margin: 0 0 4px;
}

.metric-period {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0 0 16px;
}

.chart-container {
    height: 100px;
    margin-top: 16px;
}

/* Chart Legend */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.legend-dot.grouped {
    background-color: #000;
}

.legend-dot.stacked {
    background-color: #777;
}

.legend-dot.product1 {
    background-color: var(--chart-blue);
}

.legend-dot.product2 {
    background-color: var(--chart-purple);
}

.legend-dot.product3 {
    background-color: var(--chart-green);
}

.legend-text {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Report Item */
.report-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
}

.report-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.report-content {
    flex: 1;
}

.report-title {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px;
}

.report-desc {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

.report-time {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
}

.see-all {
    color: var(--primary-color);
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
}

/* Server Metrics */
.server-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.server-metric {
    text-align: center;
}

.metric-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Notification Panel */
.notification-panel, .chat-panel {
    position: fixed;
    top: 0;
    right: -320px;
    width: 320px;
    height: 100vh;
    background-color: var(--surface-color);
    box-shadow: -2px 0 5px var(--shadow-color);
    z-index: 101;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.notification-panel.panel-open, .chat-panel.panel-open {
    right: 0;
}

.panel-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.panel-header h3 {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    color: var(--text-secondary);
}

.panel-content {
    padding: 16px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.notification-icon.green {
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--chart-green);
}

.notification-icon.purple {
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
}

.notification-icon.red {
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--chart-red);
}

.notification-icon.blue {
    background-color: rgba(66, 133, 244, 0.1);
    color: var(--chart-blue);
}

.notification-content h4 {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px;
}

.notification-content h4 span {
    font-weight: 400;
    color: var(--text-secondary);
}

.notification-content p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Chat Panel */
.panel-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab {
    flex: 1;
    text-align: center;
    padding: 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.tab.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.chat-header {
    font-size: 12px;
    font-weight: 500;
    margin: 16px 0;
    color: var(--text-secondary);
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 16px;
}

.chat-content h4 {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px;
}

.chat-content p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

.chat-actions {
    position: fixed;
    bottom: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
}

.chat-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .downloads-card {
        grid-column: span 2;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .downloads-card, .reports-card, .server-card {
        grid-column: span 1;
    }

    .header-center {
        display: none;
    }
}

@media (max-width: 576px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .page-actions {
        margin-top: 16px;
    }

    .notification-panel, .chat-panel {
        width: 100%;
        right: -100%;
    }
}
