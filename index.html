<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Tutorial</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.0/css/bootstrap.min.css">
    <!-- Feather Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.28.0/feather.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        .tutorial-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }
        
        .section {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 2rem;
            transition: transform 0.2s ease-in-out;
        }
        
        .section-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 80%;
        }
        
        /* Enhanced Section Colors */
        .section-1 {
            background-color: #e6f7ff; /* Light blue */
            z-index: 3;
        }
        
        .section-2 {
            background-color: #f0f8ec; /* Light green */
            transform: translateX(100%);
            z-index: 2;
        }
        
        .section-3 {
            background-color: #fff2e6; /* Light orange */
            transform: translateX(100%);
            z-index: 1;
        }
        
        .section-4 {
            background-color: #f5e6ff; /* Light purple */
            transform: translateX(100%);
            z-index: 0;
        }
        
        .text-column {
            flex: 1;
            padding: 2rem;
        }
        
        .image-column {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }
        
        .image-column img {
            max-width: 100%;
            max-height: 400px;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #333;
        }
        
        .section-description {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #555;
        }
        
        .tutorial-controls {
            position: absolute;
            bottom: 5rem;
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 1rem;
            z-index: 10;
        }
        
        .control-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .prev-btn {
            background-color: #f8f9fa;
            color: #007bff;
            border: 2px solid #007bff;
        }
        
        .next-btn {
            background-color: #007bff;
            color: white;
            border: 2px solid #007bff;
        }
        
        .prev-btn:hover, .next-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }
        
        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .progress-bar-container {
            position: absolute;
            bottom: 1rem;
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .progress-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .progress-dot.active {
            background-color: #007bff;
            transform: scale(1.3);
        }
        
        /* Side Navigation Chevrons */
        .side-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .side-nav:hover {
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .side-nav.prev {
            left: 20px;
        }
        
        .side-nav.next {
            right: 20px;
        }
        
        .side-nav i {
            font-size: 24px;
            color: #007bff;
        }
        
        .side-nav.disabled {
            opacity: 0.3;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        /* Matching colors for section indicators */
        .progress-dot[data-index="0"].active {
            background-color: #0099ff; /* Blue matching section 1 */
        }
        
        .progress-dot[data-index="1"].active {
            background-color: #4caf50; /* Green matching section 2 */
        }
        
        .progress-dot[data-index="2"].active {
            background-color: #ff9800; /* Orange matching section 3 */
        }
        
        .progress-dot[data-index="3"].active {
            background-color: #9c27b0; /* Purple matching section 4 */
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .section-content {
                flex-direction: column;
                overflow-y: auto;
            }
            
            .text-column, .image-column {
                flex: none;
                width: 100%;
                padding: 1rem;
            }
            
            .section-title {
                font-size: 2rem;
                text-align: center;
            }
            
            .image-column img {
                max-height: 250px;
            }
            
            .side-nav {
                width: 40px;
                height: 40px;
            }
            
            .side-nav.prev {
                left: 10px;
            }
            
            .side-nav.next {
                right: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="tutorial-container">
        <!-- Side Navigation Chevrons -->
        <div class="side-nav prev disabled" id="sideNavPrev">
            <i data-feather="chevron-left"></i>
        </div>
        <div class="side-nav next" id="sideNavNext">
            <i data-feather="chevron-right"></i>
        </div>
        
        <!-- Section 1 -->
        <div class="section section-1">
            <div class="section-content">
                <div class="text-column">
                    <h2 class="section-title">Welcome to Our App</h2>
                    <p class="section-description">
                        Get started with our powerful application designed to boost your productivity.
                        This tutorial will guide you through all the essential features and help you
                        make the most of our app. Swipe through to learn more about what our app can do for you.
                    </p>
                    <div class="mt-4">
                        <ul class="section-description">
                            <li>Easy to use interface</li>
                            <li>Powerful productivity tools</li>
                            <li>Real-time collaboration</li>
                            <li>Cross-platform support</li>
                        </ul>
                    </div>
                </div>
                <div class="image-column">
                    <img src="https://42f2671d685f51e10fc6-b9fcecea3e50b3b59bdc28dead054ebc.ssl.cf5.rackcdn.com/illustrations/Mobile_application_mr4r.svg" alt="App Welcome" />
                </div>
            </div>
        </div>

        <!-- Section 2 -->
        <div class="section section-2">
            <div class="section-content">
                <div class="text-column">
                    <h2 class="section-title">Dashboard Overview</h2>
                    <p class="section-description">
                        Your dashboard is the command center of the app. Here you can view all your projects,
                        track progress, and access key features with just a tap. The intuitive interface
                        makes navigation simple, allowing you to focus on what matters most - your work.
                    </p>
                    <p class="section-description">
                        Try customizing your dashboard by dragging and dropping widgets to create
                        the perfect workspace for your needs.
                    </p>
                </div>
                <div class="image-column">
                    <img src="https://42f2671d685f51e10fc6-b9fcecea3e50b3b59bdc28dead054ebc.ssl.cf5.rackcdn.com/illustrations/Dashboard_re_3b76.svg" alt="Dashboard Overview" />
                </div>
            </div>
        </div>

        <!-- Section 3 -->
        <div class="section section-3">
            <div class="section-content">
                <div class="text-column">
                    <h2 class="section-title">Creating Your First Project</h2>
                    <p class="section-description">
                        Starting a new project is simple. Click the "+" button in the top right
                        corner of your dashboard to begin. Give your project a name, set deadlines,
                        and invite team members to collaborate.
                    </p>
                    <p class="section-description">
                        You can choose from various templates or start from scratch. Our intelligent
                        assistant will guide you through the setup process to ensure you're ready to go in minutes.
                    </p>
                </div>
                <div class="image-column">
                    <img src="https://42f2671d685f51e10fc6-b9fcecea3e50b3b59bdc28dead054ebc.ssl.cf5.rackcdn.com/illustrations/Progress_tracking_re_ulfg.svg" alt="Creating Project" />
                </div>
            </div>
        </div>

        <!-- Section 4 -->
        <div class="section section-4">
            <div class="section-content">
                <div class="text-column">
                    <h2 class="section-title">Collaboration & Sharing</h2>
                    <p class="section-description">
                        Work together with your team in real-time. Share projects, assign tasks,
                        and communicate directly within the app. All changes sync instantly across
                        all devices, keeping everyone on the same page.
                    </p>
                    <p class="section-description">
                        Advanced permission settings let you control who can view or edit your projects,
                        ensuring data security while enabling seamless collaboration.
                    </p>
                </div>
                <div class="image-column">
                    <img src="https://42f2671d685f51e10fc6-b9fcecea3e50b3b59bdc28dead054ebc.ssl.cf5.rackcdn.com/illustrations/Team_collaboration_re_ow29.svg" alt="Collaboration" />
                </div>
            </div>
        </div>

        <!-- Navigation Controls -->
        <div class="tutorial-controls">
            <button class="btn control-btn prev-btn" id="prevBtn" disabled>
                <i data-feather="arrow-left" class="mr-1"></i> Previous
            </button>
            <button class="btn control-btn next-btn" id="nextBtn">
                Next <i data-feather="arrow-right" class="ml-1"></i>
            </button>
        </div>

        <!-- Progress Indicator -->
        <div class="progress-bar-container">
            <div class="progress-dot active" data-index="0"></div>
            <div class="progress-dot" data-index="1"></div>
            <div class="progress-dot" data-index="2"></div>
            <div class="progress-dot" data-index="3"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.0/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.28.0/feather.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather Icons
            feather.replace();
            
            const sections = document.querySelectorAll('.section');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const sideNavPrev = document.getElementById('sideNavPrev');
            const sideNavNext = document.getElementById('sideNavNext');
            const progressDots = document.querySelectorAll('.progress-dot');
            let currentIndex = 0;
            
            // Initialize
            updateButtons();
            
            // Navigation function
            function goToSection(direction) {
                if (direction === 'next' && currentIndex < sections.length - 1) {
                    // Move current section left
                    sections[currentIndex].style.transform = 'translateX(-100%)';
                    currentIndex++;
                    
                    // Move next section to center
                    sections[currentIndex].style.transform = 'translateX(0)';
                    
                    // Update UI
                    updateButtons();
                    updateProgressDots();
                } else if (direction === 'prev' && currentIndex > 0) {
                    // Move current section right
                    sections[currentIndex].style.transform = 'translateX(100%)';
                    currentIndex--;
                    
                    // Move previous section to center
                    sections[currentIndex].style.transform = 'translateX(0)';
                    
                    // Update UI
                    updateButtons();
                    updateProgressDots();
                }
            }
            
            // Next button click
            nextBtn.addEventListener('click', function() {
                goToSection('next');
            });
            
            // Previous button click
            prevBtn.addEventListener('click', function() {
                goToSection('prev');
            });
            
            // Side Navigation - Next
            sideNavNext.addEventListener('click', function() {
                goToSection('next');
            });
            
            // Side Navigation - Previous
            sideNavPrev.addEventListener('click', function() {
                goToSection('prev');
            });
            
            // Progress dots click
            progressDots.forEach(dot => {
                dot.addEventListener('click', function() {
                    const targetIndex = parseInt(this.getAttribute('data-index'));
                    
                    if (targetIndex !== currentIndex) {
                        // Determine direction
                        const direction = targetIndex > currentIndex ? 'next' : 'prev';
                        
                        // Move current section
                        sections[currentIndex].style.transform = direction === 'next' ? 
                            'translateX(-100%)' : 'translateX(100%)';
                        
                        // Move target section
                        sections[targetIndex].style.transform = 'translateX(0)';
                        
                        // Update current index
                        currentIndex = targetIndex;
                        
                        // Update UI
                        updateButtons();
                        updateProgressDots();
                    }
                });
            });
            
            // Helper functions
            function updateButtons() {
                // Update bottom navigation buttons
                prevBtn.disabled = currentIndex === 0;
                nextBtn.disabled = currentIndex === sections.length - 1;
                
                // Update side navigation chevrons
                if (currentIndex === 0) {
                    sideNavPrev.classList.add('disabled');
                } else {
                    sideNavPrev.classList.remove('disabled');
                }
                
                if (currentIndex === sections.length - 1) {
                    sideNavNext.classList.add('disabled');
                } else {
                    sideNavNext.classList.remove('disabled');
                }
            }
            
            function updateProgressDots() {
                progressDots.forEach((dot, index) => {
                    if (index === currentIndex) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>
</html>