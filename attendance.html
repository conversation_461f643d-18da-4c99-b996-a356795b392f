<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance - Material Design</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="list-styles.css">
    <link rel="stylesheet" href="attendance-styles.css">
    <link rel="stylesheet" href="ripple.css">
</head>
<body>
    <div x-data="attendanceList()" class="app-container">
        <!-- Navigation Drawer -->
        <nav class="nav-drawer" :class="{'drawer-open': drawerOpen}">
            <div class="drawer-header">
                <div class="app-info">
                    <div class="app-icon">
                        <span class="material-icons">school</span>
                    </div>
                    <div class="app-details">
                        <h3>EduManager</h3>
                        <p>Student Management System</p>
                    </div>
                </div>
                <button class="close-drawer" @click="closeDrawer">
                    <span class="material-icons">close</span>
                </button>
            </div>
            
            <div class="drawer-content">
                <div class="nav-section">
                    <h4 class="nav-section-title">Main</h4>
                    <a href="dashboard.html" class="nav-item">
                        <span class="material-icons">dashboard</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="list.html" class="nav-item">
                        <span class="material-icons">people</span>
                        <span class="nav-text">Students</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Academic</h4>
                    <a href="grades.html" class="nav-item">
                        <span class="material-icons">grade</span>
                        <span class="nav-text">Grades</span>
                    </a>
                    <a href="attendance.html" class="nav-item active">
                        <span class="material-icons">event_available</span>
                        <span class="nav-text">Attendance</span>
                    </a>
                    <a href="courses.html" class="nav-item">
                        <span class="material-icons">book</span>
                        <span class="nav-text">Courses</span>
                    </a>
                    <a href="schedule.html" class="nav-item">
                        <span class="material-icons">schedule</span>
                        <span class="nav-text">Schedule</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Financial</h4>
                    <a href="payments.html" class="nav-item">
                        <span class="material-icons">payment</span>
                        <span class="nav-text">Payments</span>
                    </a>
                    <a href="fees.html" class="nav-item">
                        <span class="material-icons">account_balance</span>
                        <span class="nav-text">Fee Structure</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Communication</h4>
                    <a href="messages.html" class="nav-item">
                        <span class="material-icons">message</span>
                        <span class="nav-text">Messages</span>
                    </a>
                    <a href="notifications.html" class="nav-item">
                        <span class="material-icons">notifications</span>
                        <span class="nav-text">Notifications</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Reports</h4>
                    <a href="analytics.html" class="nav-item">
                        <span class="material-icons">analytics</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                    <a href="reports.html" class="nav-item">
                        <span class="material-icons">assessment</span>
                        <span class="nav-text">Reports</span>
                    </a>
                </div>
            </div>
            
            <div class="drawer-footer">
                <a href="settings.html" class="nav-item">
                    <span class="material-icons">settings</span>
                    <span class="nav-text">Settings</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <span class="material-icons">account_circle</span>
                    <span class="nav-text">Profile</span>
                </a>
            </div>
        </nav>
        
        <!-- Drawer Overlay -->
        <div class="drawer-overlay" :class="{'overlay-active': drawerOpen}" @click="closeDrawer"></div>

        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <button class="menu-btn" @click="toggleDrawer">
                    <span class="material-icons">menu</span>
                </button>
                <h1 class="page-title">Attendance</h1>
                <div class="header-actions">
                    <button class="action-btn" @click="toggleSearch">
                        <span class="material-icons">search</span>
                    </button>
                    <button class="action-btn" @click="showDatePicker">
                        <span class="material-icons">date_range</span>
                    </button>
                    <button class="action-btn">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-container" :class="{'search-active': searchActive}">
                <div class="search-box">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" placeholder="Search attendance..." class="search-input" x-model="searchQuery">
                    <button class="clear-search" @click="clearSearch" x-show="searchQuery">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Date Navigation -->
        <div class="date-navigation">
            <button class="date-nav-btn" @click="previousDate">
                <span class="material-icons">chevron_left</span>
            </button>
            <div class="current-date">
                <h3 x-text="formatDate(currentDate)"></h3>
                <p x-text="getDayOfWeek(currentDate)"></p>
            </div>
            <button class="date-nav-btn" @click="nextDate">
                <span class="material-icons">chevron_right</span>
            </button>
        </div>

        <!-- Attendance Summary -->
        <div class="attendance-summary">
            <div class="summary-card present">
                <div class="summary-icon">
                    <span class="material-icons">check_circle</span>
                </div>
                <div class="summary-info">
                    <h4 x-text="getPresentCount()"></h4>
                    <p>Present</p>
                </div>
            </div>
            <div class="summary-card absent">
                <div class="summary-icon">
                    <span class="material-icons">cancel</span>
                </div>
                <div class="summary-info">
                    <h4 x-text="getAbsentCount()"></h4>
                    <p>Absent</p>
                </div>
            </div>
            <div class="summary-card late">
                <div class="summary-icon">
                    <span class="material-icons">schedule</span>
                </div>
                <div class="summary-info">
                    <h4 x-text="getLateCount()"></h4>
                    <p>Late</p>
                </div>
            </div>
            <div class="summary-card total">
                <div class="summary-icon">
                    <span class="material-icons">people</span>
                </div>
                <div class="summary-info">
                    <h4 x-text="getTotalCount()"></h4>
                    <p>Total</p>
                </div>
            </div>
        </div>

        <!-- Filter Chips -->
        <div class="filter-container">
            <div class="filter-chips">
                <button class="filter-chip" :class="{'active': filter === 'all'}" @click="setFilter('all')">
                    All Students
                </button>
                <button class="filter-chip" :class="{'active': filter === 'present'}" @click="setFilter('present')">
                    Present
                </button>
                <button class="filter-chip" :class="{'active': filter === 'absent'}" @click="setFilter('absent')">
                    Absent
                </button>
                <button class="filter-chip" :class="{'active': filter === 'late'}" @click="setFilter('late')">
                    Late
                </button>
            </div>
        </div>

        <!-- Attendance List -->
        <div class="attendance-container">
            <template x-for="record in filteredAttendance" :key="record.id">
                <div class="attendance-card" @click="selectRecord(record)">
                    <div class="attendance-media">
                        <div class="student-avatar" :style="'background-color: ' + record.avatarColor">
                            <span class="material-icons">person</span>
                        </div>
                        <div class="attendance-status" :class="record.status">
                            <span class="material-icons" x-text="getStatusIcon(record.status)"></span>
                        </div>
                    </div>
                    
                    <div class="attendance-content">
                        <div class="attendance-info">
                            <h3 class="student-name" x-text="record.studentName"></h3>
                            <p class="student-id" x-text="'ID: ' + record.studentId"></p>
                        </div>
                        
                        <div class="attendance-details">
                            <div class="attendance-row">
                                <span class="attendance-label">Status:</span>
                                <span class="attendance-value" :class="record.status" x-text="record.status.charAt(0).toUpperCase() + record.status.slice(1)"></span>
                            </div>
                            <div class="attendance-row" x-show="record.checkInTime">
                                <span class="attendance-label">Check-in:</span>
                                <span class="attendance-time" x-text="record.checkInTime"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="attendance-actions">
                        <button class="action-btn-small" @click.stop="markPresent(record)" title="Mark Present" :disabled="record.status === 'present'">
                            <span class="material-icons">check</span>
                        </button>
                        <button class="action-btn-small" @click.stop="markAbsent(record)" title="Mark Absent" :disabled="record.status === 'absent'">
                            <span class="material-icons">close</span>
                        </button>
                        <button class="action-btn-small" @click.stop="showMore(record)" title="More">
                            <span class="material-icons">more_horiz</span>
                        </button>
                    </div>
                </div>
            </template>
            
            <!-- Empty State -->
            <div class="empty-state" x-show="filteredAttendance.length === 0">
                <div class="empty-icon">
                    <span class="material-icons">event_available</span>
                </div>
                <h3>No attendance records found</h3>
                <p>Try adjusting your search or filter criteria</p>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="fab" @click="markAllPresent" title="Mark All Present">
            <span class="material-icons">done_all</span>
        </button>

        <!-- Bottom Sheet for Actions -->
        <div class="bottom-sheet" :class="{'active': bottomSheetActive}" @click="closeBottomSheet">
            <div class="bottom-sheet-content" @click.stop>
                <div class="bottom-sheet-header">
                    <h3 x-text="selectedRecord?.studentName"></h3>
                    <button class="close-btn" @click="closeBottomSheet">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="bottom-sheet-actions">
                    <button class="sheet-action" @click="markPresent(selectedRecord)">
                        <span class="material-icons">check_circle</span>
                        Mark Present
                    </button>
                    <button class="sheet-action" @click="markAbsent(selectedRecord)">
                        <span class="material-icons">cancel</span>
                        Mark Absent
                    </button>
                    <button class="sheet-action" @click="markLate(selectedRecord)">
                        <span class="material-icons">schedule</span>
                        Mark Late
                    </button>
                    <button class="sheet-action" @click="viewHistory">
                        <span class="material-icons">history</span>
                        View History
                    </button>
                    <button class="sheet-action" @click="sendNotification">
                        <span class="material-icons">notifications</span>
                        Send Notification
                    </button>
                </div>
            </div>
        </div>

        <!-- Overlay -->
        <div class="overlay" :class="{'active': bottomSheetActive}" @click="closeBottomSheet"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Custom JS -->
    <script src="ripple.js"></script>
    <script src="attendance-app.js"></script>
</body>
</html>
