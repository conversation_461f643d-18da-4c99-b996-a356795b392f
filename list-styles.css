/* Material Design Students List Styles */

:root {
    --primary-color: #6200ee;
    --primary-light: #9c4dff;
    --primary-dark: #3700b3;
    --secondary-color: #03dac6;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --error-color: #b00020;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --text-primary: rgba(0, 0, 0, 0.87);
    --text-secondary: rgba(0, 0, 0, 0.6);
    --text-disabled: rgba(0, 0, 0, 0.38);
    --text-light: #ffffff;
    --border-color: rgba(0, 0, 0, 0.12);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --elevation-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --elevation-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --elevation-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 14px;
    overflow-x: hidden;
}

.app-container {
    min-height: 100vh;
    position: relative;
}

/* Navigation Drawer */
.nav-drawer {
    position: fixed;
    top: 0;
    left: -320px;
    width: 320px;
    height: 100vh;
    background-color: var(--surface-color);
    box-shadow: var(--elevation-3);
    z-index: 300;
    transition: left 0.3s ease;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.nav-drawer.drawer-open {
    left: 0;
}

.drawer-header {
    padding: 24px 16px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.app-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.app-icon {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
}

.app-details h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    color: var(--text-primary);
}

.app-details p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

.close-drawer {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-drawer:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.drawer-content {
    flex: 1;
    padding: 8px 0;
}

.nav-section {
    margin-bottom: 16px;
}

.nav-section-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 16px;
    margin: 0 0 4px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s;
    border-radius: 0 24px 24px 0;
    margin-right: 8px;
}

.nav-item:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: var(--text-primary);
}

.nav-item.active {
    background-color: rgba(98, 0, 238, 0.08);
    color: var(--primary-color);
}

.nav-item .material-icons {
    font-size: 24px;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

.drawer-footer {
    border-top: 1px solid var(--border-color);
    padding: 8px 0;
}

.drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 250;
}

.drawer-overlay.overlay-active {
    opacity: 1;
    visibility: visible;
}

/* Header Styles */
.header {
    background-color: var(--primary-color);
    color: var(--text-light);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--elevation-2);
}

.header-content {
    display: flex;
    align-items: center;
    padding: 16px;
    min-height: 64px;
}

.back-btn {
    background: none;
    border: none;
    color: var(--text-light);
    margin-right: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.page-title {
    flex: 1;
    font-size: 20px;
    font-weight: 500;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Search Container */
.search-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0 16px;
}

.search-container.search-active {
    max-height: 80px;
    padding: 16px;
}

.search-box {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 28px;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    position: relative;
}

.search-icon {
    color: var(--text-light);
    margin-right: 12px;
}

.search-input {
    background: none;
    border: none;
    color: var(--text-light);
    outline: none;
    width: 100%;
    font-size: 16px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.clear-search {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
}

/* Filter Container */
.filter-container {
    padding: 16px;
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
}

.filter-chips {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 4px;
}

.filter-chip {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s;
}

.filter-chip.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.filter-chip:hover:not(.active) {
    background-color: rgba(98, 0, 238, 0.08);
}

/* Students Container */
.students-container {
    padding: 8px 16px 80px;
}

/* Student Card */
.student-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: var(--elevation-1);
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.student-card:hover {
    box-shadow: var(--elevation-2);
    transform: translateY(-1px);
}

.student-card:active {
    transform: translateY(0);
    box-shadow: var(--elevation-1);
}

/* Student Media */
.student-media {
    position: relative;
    flex-shrink: 0;
}

.student-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 24px;
    overflow: hidden;
}

.student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.student-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--surface-color);
    font-size: 12px;
}

.student-status.paid {
    background-color: var(--success-color);
    color: var(--text-light);
}

.student-status.pending {
    background-color: var(--warning-color);
    color: var(--text-light);
}

.student-status.overdue {
    background-color: var(--error-color);
    color: var(--text-light);
}

/* Student Content */
.student-content {
    flex: 1;
    min-width: 0;
}

.student-info {
    margin-bottom: 8px;
}

.student-name {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 4px;
    color: var(--text-primary);
}

.student-id {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

.payment-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.payment-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.payment-amount {
    font-size: 14px;
    font-weight: 500;
}

.payment-amount.paid {
    color: var(--success-color);
}

.payment-amount.remaining {
    color: var(--error-color);
}

/* Student Actions */
.student-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
}

.action-btn-small {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn-small:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: var(--text-primary);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.empty-state p {
    margin-bottom: 24px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    padding: 12px 24px;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    box-shadow: var(--elevation-3);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.2s;
    z-index: 50;
}

.fab:hover {
    background-color: var(--primary-dark);
    transform: scale(1.1);
}

.fab:active {
    transform: scale(0.95);
}

/* Bottom Sheet */
.bottom-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--surface-color);
    border-radius: 16px 16px 0 0;
    box-shadow: var(--elevation-3);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 200;
    max-height: 70vh;
    overflow: hidden;
}

.bottom-sheet.active {
    transform: translateY(0);
}

.bottom-sheet-content {
    padding: 0;
}

.bottom-sheet-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}

.bottom-sheet-header h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.bottom-sheet-actions {
    padding: 8px 0;
}

.sheet-action {
    width: 100%;
    background: none;
    border: none;
    padding: 16px 24px;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 16px;
    color: var(--text-primary);
    transition: background-color 0.2s;
}

.sheet-action:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.sheet-action.danger {
    color: var(--error-color);
}

.sheet-action .material-icons {
    font-size: 24px;
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 150;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Student Detail Modal */
.student-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-color);
    z-index: 400;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.student-detail-modal.modal-active {
    transform: translateX(0);
}

.detail-header {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: var(--elevation-2);
}

.detail-header .back-btn,
.detail-header .edit-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.detail-header .back-btn:hover,
.detail-header .edit-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.detail-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    flex: 1;
    text-align: center;
}

.detail-content {
    padding: 0 0 80px;
}

/* Profile Section */
.profile-section {
    background-color: var(--surface-color);
    padding: 24px 16px;
    margin-bottom: 8px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
}

.profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.student-avatar.large {
    width: 80px;
    height: 80px;
    font-size: 32px;
}

.profile-status {
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid var(--surface-color);
    font-size: 14px;
}

.profile-info {
    flex: 1;
}

.profile-info .student-name {
    font-size: 24px;
    font-weight: 500;
    margin: 0 0 4px;
    color: var(--text-primary);
}

.profile-info .student-id {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0 0 12px;
}

.profile-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.grade-badge {
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
}

.status-badge.paid {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-badge.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.status-badge.overdue {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

/* Info Sections */
.info-section {
    background-color: var(--surface-color);
    margin-bottom: 8px;
    padding: 20px 16px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 16px;
    color: var(--text-primary);
}

.section-title .material-icons {
    color: var(--primary-color);
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 100px;
}

.info-value {
    font-size: 14px;
    color: var(--text-primary);
    text-align: right;
    flex: 1;
    word-break: break-word;
}

/* Performance Summary */
.performance-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
}

.performance-card {
    background-color: var(--background-color);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.performance-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.performance-info h4 {
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.performance-info p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 480px) {
    .student-card {
        padding: 12px;
        gap: 12px;
    }

    .student-avatar {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .student-actions {
        flex-direction: row;
        gap: 2px;
    }

    .action-btn-small {
        width: 28px;
        height: 28px;
        font-size: 18px;
    }

    .filter-chips {
        gap: 6px;
    }

    .filter-chip {
        padding: 6px 12px;
        font-size: 12px;
    }

    .fab {
        bottom: 16px;
        right: 16px;
        width: 48px;
        height: 48px;
        font-size: 20px;
    }
}

@media (max-width: 360px) {
    .students-container {
        padding: 8px 12px 80px;
    }

    .header-content {
        padding: 12px;
    }

    .filter-container {
        padding: 12px;
    }

    .student-card {
        padding: 10px;
        gap: 10px;
    }

    .student-name {
        font-size: 15px;
    }

    .payment-amount {
        font-size: 13px;
    }
}
