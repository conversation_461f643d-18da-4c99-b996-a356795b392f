// Alpine.js Students List Component
function studentsList() {
    return {
        drawerOpen: false,
        searchActive: false,
        searchQuery: '',
        filter: 'all',
        bottomSheetActive: false,
        selectedStudent: null,
        detailModalActive: false,
        selectedStudentDetail: null,
        
        // Sample student data
        students: [
            {
                id: 1,
                name: '<PERSON>',
                studentId: 'STU001',
                photo: null,
                avatarColor: '#e91e63',
                amountPaid: 1200,
                amountRemaining: 300,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8901',
                dateOfBirth: 'March 15, 2005',
                age: 18,
                gender: 'Female',
                address: '123 Oak Street, Springfield, IL 62701',
                guardian: '<PERSON> (Mother)',
                grade: '12th',
                overallGPA: '3.8',
                classRank: '15/120',
                attendanceRate: 94,
                totalFees: 1500,
                lastPaymentDate: 'January 15, 2024',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.9',
                        subjects: [
                            { name: 'Mathematics', grade: 'A' },
                            { name: 'English', grade: 'A-' },
                            { name: 'Physics', grade: 'B+' },
                            { name: 'Chemistry', grade: 'A' },
                            { name: 'History', grade: 'B' }
                        ]
                    },
                    {
                        term: 'Spring 2023',
                        gpa: '3.7',
                        subjects: [
                            { name: 'Mathematics', grade: 'B+' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'B' },
                            { name: 'Chemistry', grade: 'A-' },
                            { name: 'History', grade: 'B+' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 142,
                    absent: 8,
                    late: 5,
                    excused: 3
                },
                emergencyContacts: [
                    {
                        name: 'Sarah Johnson',
                        relationship: 'Mother',
                        phone: '****** 567 8901'
                    },
                    {
                        name: 'Michael Johnson',
                        relationship: 'Father',
                        phone: '****** 567 8902'
                    }
                ]
            },
            {
                id: 2,
                name: 'Michael Chen',
                studentId: 'STU002',
                photo: null,
                avatarColor: '#2196f3',
                amountPaid: 1500,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8902',
                dateOfBirth: 'July 22, 2004',
                age: 19,
                gender: 'Male',
                address: '456 Pine Avenue, Springfield, IL 62702',
                guardian: 'Li Chen (Father)',
                grade: '12th',
                overallGPA: '4.0',
                classRank: '1/120',
                attendanceRate: 98,
                totalFees: 1500,
                lastPaymentDate: 'December 20, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '4.0',
                        subjects: [
                            { name: 'Mathematics', grade: 'A+' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'A+' },
                            { name: 'Chemistry', grade: 'A' },
                            { name: 'Computer Science', grade: 'A+' }
                        ]
                    },
                    {
                        term: 'Spring 2023',
                        gpa: '4.0',
                        subjects: [
                            { name: 'Mathematics', grade: 'A+' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'A' },
                            { name: 'Chemistry', grade: 'A+' },
                            { name: 'Computer Science', grade: 'A+' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 155,
                    absent: 2,
                    late: 1,
                    excused: 0
                },
                emergencyContacts: [
                    {
                        name: 'Li Chen',
                        relationship: 'Father',
                        phone: '****** 567 8902'
                    },
                    {
                        name: 'Wei Chen',
                        relationship: 'Mother',
                        phone: '****** 567 8903'
                    }
                ]
            },
            {
                id: 3,
                name: 'Sarah Williams',
                studentId: 'STU003',
                photo: null,
                avatarColor: '#4caf50',
                amountPaid: 800,
                amountRemaining: 700,
                paymentStatus: 'overdue',
                email: '<EMAIL>',
                phone: '****** 567 8903',
                dateOfBirth: 'November 8, 2005',
                age: 18,
                gender: 'Female',
                address: '789 Maple Drive, Springfield, IL 62703',
                guardian: 'Jennifer Williams (Mother)',
                grade: '11th',
                overallGPA: '3.2',
                classRank: '45/120',
                attendanceRate: 87,
                totalFees: 1500,
                lastPaymentDate: 'October 10, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.1',
                        subjects: [
                            { name: 'Mathematics', grade: 'C+' },
                            { name: 'English', grade: 'B' },
                            { name: 'Biology', grade: 'B-' },
                            { name: 'History', grade: 'C' },
                            { name: 'Art', grade: 'A' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 130,
                    absent: 18,
                    late: 8,
                    excused: 2
                },
                emergencyContacts: [
                    {
                        name: 'Jennifer Williams',
                        relationship: 'Mother',
                        phone: '****** 567 8903'
                    }
                ]
            },
            {
                id: 4,
                name: 'David Rodriguez',
                studentId: 'STU004',
                photo: null,
                avatarColor: '#ff9800',
                amountPaid: 1000,
                amountRemaining: 500,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8904',
                dateOfBirth: 'September 12, 2005',
                age: 18,
                gender: 'Male',
                address: '321 Cedar Lane, Springfield, IL 62704',
                guardian: 'Carlos Rodriguez (Father)',
                grade: '11th',
                overallGPA: '3.4',
                classRank: '35/120',
                attendanceRate: 91,
                totalFees: 1500,
                lastPaymentDate: 'November 15, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.4',
                        subjects: [
                            { name: 'Mathematics', grade: 'B' },
                            { name: 'English', grade: 'B+' },
                            { name: 'Science', grade: 'B-' },
                            { name: 'History', grade: 'B' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 138,
                    absent: 12,
                    late: 6,
                    excused: 2
                },
                emergencyContacts: [
                    {
                        name: 'Carlos Rodriguez',
                        relationship: 'Father',
                        phone: '****** 567 8904'
                    }
                ]
            },
            {
                id: 5,
                name: 'Lisa Thompson',
                studentId: 'STU005',
                photo: null,
                avatarColor: '#9c27b0',
                amountPaid: 1800,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8905',
                dateOfBirth: 'February 28, 2004',
                age: 19,
                gender: 'Female',
                address: '654 Birch Street, Springfield, IL 62705',
                guardian: 'Nancy Thompson (Mother)',
                grade: '12th',
                overallGPA: '3.9',
                classRank: '8/120',
                attendanceRate: 96,
                totalFees: 1800,
                lastPaymentDate: 'January 5, 2024',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.9',
                        subjects: [
                            { name: 'Mathematics', grade: 'A-' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'A-' },
                            { name: 'Chemistry', grade: 'B+' },
                            { name: 'Art', grade: 'A+' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 148,
                    absent: 4,
                    late: 3,
                    excused: 3
                },
                emergencyContacts: [
                    {
                        name: 'Nancy Thompson',
                        relationship: 'Mother',
                        phone: '****** 567 8905'
                    }
                ]
            },
            {
                id: 6,
                name: 'James Anderson',
                studentId: 'STU006',
                photo: null,
                avatarColor: '#f44336',
                amountPaid: 600,
                amountRemaining: 900,
                paymentStatus: 'overdue',
                email: '<EMAIL>',
                phone: '****** 567 8906',
                dateOfBirth: 'June 5, 2005',
                age: 18,
                gender: 'Male',
                address: '987 Elm Avenue, Springfield, IL 62706',
                guardian: 'Robert Anderson (Father)',
                grade: '11th',
                overallGPA: '2.8',
                classRank: '78/120',
                attendanceRate: 82,
                totalFees: 1500,
                lastPaymentDate: 'August 20, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '2.8',
                        subjects: [
                            { name: 'Mathematics', grade: 'C' },
                            { name: 'English', grade: 'C+' },
                            { name: 'Science', grade: 'C-' },
                            { name: 'History', grade: 'D+' },
                            { name: 'PE', grade: 'B' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 125,
                    absent: 22,
                    late: 12,
                    excused: 1
                },
                emergencyContacts: [
                    {
                        name: 'Robert Anderson',
                        relationship: 'Father',
                        phone: '****** 567 8906'
                    }
                ]
            },
            {
                id: 7,
                name: 'Maria Garcia',
                studentId: 'STU007',
                photo: null,
                avatarColor: '#00bcd4',
                amountPaid: 1300,
                amountRemaining: 200,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8907',
                dateOfBirth: 'April 18, 2005',
                age: 18,
                gender: 'Female',
                address: '147 Willow Road, Springfield, IL 62707',
                guardian: 'Isabella Garcia (Mother)',
                grade: '12th',
                overallGPA: '3.6',
                classRank: '25/120',
                attendanceRate: 93,
                totalFees: 1500,
                lastPaymentDate: 'December 10, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.6',
                        subjects: [
                            { name: 'Mathematics', grade: 'B+' },
                            { name: 'English', grade: 'A-' },
                            { name: 'Biology', grade: 'B+' },
                            { name: 'Spanish', grade: 'A' },
                            { name: 'History', grade: 'B' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 142,
                    absent: 8,
                    late: 4,
                    excused: 4
                },
                emergencyContacts: [
                    {
                        name: 'Isabella Garcia',
                        relationship: 'Mother',
                        phone: '****** 567 8907'
                    }
                ]
            },
            {
                id: 8,
                name: 'Robert Taylor',
                studentId: 'STU008',
                photo: null,
                avatarColor: '#795548',
                amountPaid: 2000,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8908',
                dateOfBirth: 'December 3, 2004',
                age: 19,
                gender: 'Male',
                address: '258 Spruce Circle, Springfield, IL 62708',
                guardian: 'Helen Taylor (Mother)',
                grade: '12th',
                overallGPA: '3.7',
                classRank: '18/120',
                attendanceRate: 95,
                totalFees: 2000,
                lastPaymentDate: 'January 1, 2024',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.7',
                        subjects: [
                            { name: 'Mathematics', grade: 'A-' },
                            { name: 'English', grade: 'B+' },
                            { name: 'Physics', grade: 'A' },
                            { name: 'Computer Science', grade: 'A' },
                            { name: 'Economics', grade: 'B+' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 146,
                    absent: 6,
                    late: 2,
                    excused: 4
                },
                emergencyContacts: [
                    {
                        name: 'Helen Taylor',
                        relationship: 'Mother',
                        phone: '****** 567 8908'
                    }
                ]
            }
        ],

        // Computed property for filtered students
        get filteredStudents() {
            let filtered = this.students;
            
            // Apply search filter
            if (this.searchQuery.trim()) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(student => 
                    student.name.toLowerCase().includes(query) ||
                    student.studentId.toLowerCase().includes(query) ||
                    student.email.toLowerCase().includes(query)
                );
            }
            
            // Apply status filter
            if (this.filter !== 'all') {
                filtered = filtered.filter(student => student.paymentStatus === this.filter);
            }
            
            return filtered;
        },
        
        // Toggle drawer
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },

        // Close drawer
        closeDrawer() {
            this.drawerOpen = false;
        },

        // Toggle search visibility
        toggleSearch() {
            this.searchActive = !this.searchActive;
            if (!this.searchActive) {
                this.searchQuery = '';
            }
        },
        
        // Clear search
        clearSearch() {
            this.searchQuery = '';
        },
        
        // Set filter
        setFilter(filter) {
            this.filter = filter;
        },
        
        // Get status icon
        getStatusIcon(status) {
            switch (status) {
                case 'paid':
                    return 'check_circle';
                case 'pending':
                    return 'schedule';
                case 'overdue':
                    return 'error';
                default:
                    return 'help';
            }
        },

        // Get grade class for styling
        getGradeClass(grade) {
            const gradeValue = grade.charAt(0).toLowerCase();
            return `grade-${gradeValue}`;
        },
        
        // Select student and show bottom sheet
        selectStudent(student) {
            this.selectedStudent = student;
            this.bottomSheetActive = true;
        },
        
        // Close bottom sheet
        closeBottomSheet() {
            this.bottomSheetActive = false;
            setTimeout(() => {
                this.selectedStudent = null;
            }, 300);
        },
        
        // Edit student
        editStudent(student) {
            this.closeBottomSheet();
            alert(`Edit student: ${student.name}`);
            // Here you would typically navigate to an edit form or open a modal
        },
        
        // Delete student
        deleteStudent(student) {
            if (confirm(`Are you sure you want to delete ${student.name}?`)) {
                const index = this.students.findIndex(s => s.id === student.id);
                if (index > -1) {
                    this.students.splice(index, 1);
                }
                this.closeBottomSheet();
            }
        },
        
        // Show more options
        showMore(student) {
            this.selectStudent(student);
        },
        
        // Show add student
        showAddStudent() {
            alert('Add new student');
            // Here you would typically navigate to an add form or open a modal
        },
        
        // View details
        viewDetails() {
            this.selectedStudentDetail = this.selectedStudent;
            this.detailModalActive = true;
            this.closeBottomSheet();
        },

        // Open detail modal
        openDetailModal(student) {
            this.selectedStudentDetail = student;
            this.detailModalActive = true;
        },

        // Close detail modal
        closeDetailModal() {
            this.detailModalActive = false;
            setTimeout(() => {
                this.selectedStudentDetail = null;
            }, 300);
        },
        
        // Record payment
        recordPayment() {
            this.closeBottomSheet();
            alert(`Record payment for: ${this.selectedStudent.name}`);
            // Here you would typically open a payment form
        },
        
        // Send message
        sendMessage() {
            this.closeBottomSheet();
            alert(`Send message to: ${this.selectedStudent.name}`);
            // Here you would typically open a messaging interface
        },
        
        // Initialize component
        init() {
            // Add any initialization logic here
            console.log('Students list initialized');
        }
    };
}
