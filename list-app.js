// Alpine.js Students List Component
function studentsList() {
    return {
        drawerOpen: false,
        searchActive: false,
        searchQuery: '',
        filter: 'all',
        bottomSheetActive: false,
        selectedStudent: null,
        detailModalActive: false,
        selectedStudentDetail: null,
        
        // Sample student data
        students: [
            {
                id: 1,
                name: '<PERSON>',
                studentId: 'STU001',
                photo: null,
                avatarColor: '#e91e63',
                amountPaid: 1200,
                amountRemaining: 300,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8901',
                dateOfBirth: 'March 15, 2005',
                age: 18,
                gender: 'Female',
                address: '123 Oak Street, Springfield, IL 62701',
                guardian: '<PERSON> (Mother)',
                grade: '12th',
                overallGPA: '3.8',
                classRank: '15/120',
                attendanceRate: 94,
                totalFees: 1500,
                lastPaymentDate: 'January 15, 2024',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.9',
                        subjects: [
                            { name: 'Mathematics', grade: 'A' },
                            { name: 'English', grade: 'A-' },
                            { name: 'Physics', grade: 'B+' },
                            { name: 'Chemistry', grade: 'A' },
                            { name: 'History', grade: 'B' }
                        ]
                    },
                    {
                        term: 'Spring 2023',
                        gpa: '3.7',
                        subjects: [
                            { name: 'Mathematics', grade: 'B+' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'B' },
                            { name: 'Chemistry', grade: 'A-' },
                            { name: 'History', grade: 'B+' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 142,
                    absent: 8,
                    late: 5,
                    excused: 3
                },
                emergencyContacts: [
                    {
                        name: 'Sarah Johnson',
                        relationship: 'Mother',
                        phone: '****** 567 8901'
                    },
                    {
                        name: 'Michael Johnson',
                        relationship: 'Father',
                        phone: '****** 567 8902'
                    }
                ]
            },
            {
                id: 2,
                name: 'Michael Chen',
                studentId: 'STU002',
                photo: null,
                avatarColor: '#2196f3',
                amountPaid: 1500,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8902',
                dateOfBirth: 'July 22, 2004',
                age: 19,
                gender: 'Male',
                address: '456 Pine Avenue, Springfield, IL 62702',
                guardian: 'Li Chen (Father)',
                grade: '12th',
                overallGPA: '4.0',
                classRank: '1/120',
                attendanceRate: 98,
                totalFees: 1500,
                lastPaymentDate: 'December 20, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '4.0',
                        subjects: [
                            { name: 'Mathematics', grade: 'A+' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'A+' },
                            { name: 'Chemistry', grade: 'A' },
                            { name: 'Computer Science', grade: 'A+' }
                        ]
                    },
                    {
                        term: 'Spring 2023',
                        gpa: '4.0',
                        subjects: [
                            { name: 'Mathematics', grade: 'A+' },
                            { name: 'English', grade: 'A' },
                            { name: 'Physics', grade: 'A' },
                            { name: 'Chemistry', grade: 'A+' },
                            { name: 'Computer Science', grade: 'A+' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 155,
                    absent: 2,
                    late: 1,
                    excused: 0
                },
                emergencyContacts: [
                    {
                        name: 'Li Chen',
                        relationship: 'Father',
                        phone: '****** 567 8902'
                    },
                    {
                        name: 'Wei Chen',
                        relationship: 'Mother',
                        phone: '****** 567 8903'
                    }
                ]
            },
            {
                id: 3,
                name: 'Sarah Williams',
                studentId: 'STU003',
                photo: null,
                avatarColor: '#4caf50',
                amountPaid: 800,
                amountRemaining: 700,
                paymentStatus: 'overdue',
                email: '<EMAIL>',
                phone: '****** 567 8903',
                dateOfBirth: 'November 8, 2005',
                age: 18,
                gender: 'Female',
                address: '789 Maple Drive, Springfield, IL 62703',
                guardian: 'Jennifer Williams (Mother)',
                grade: '11th',
                overallGPA: '3.2',
                classRank: '45/120',
                attendanceRate: 87,
                totalFees: 1500,
                lastPaymentDate: 'October 10, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.1',
                        subjects: [
                            { name: 'Mathematics', grade: 'C+' },
                            { name: 'English', grade: 'B' },
                            { name: 'Biology', grade: 'B-' },
                            { name: 'History', grade: 'C' },
                            { name: 'Art', grade: 'A' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 130,
                    absent: 18,
                    late: 8,
                    excused: 2
                },
                emergencyContacts: [
                    {
                        name: 'Jennifer Williams',
                        relationship: 'Mother',
                        phone: '****** 567 8903'
                    }
                ]
            },
            // Add basic detail data for remaining students
            ...this.generateBasicStudentData([
                { id: 4, name: 'David Rodriguez', studentId: 'STU004', avatarColor: '#ff9800', amountPaid: 1000, amountRemaining: 500, paymentStatus: 'pending', email: '<EMAIL>', phone: '****** 567 8904' },
                { id: 5, name: 'Lisa Thompson', studentId: 'STU005', avatarColor: '#9c27b0', amountPaid: 1800, amountRemaining: 0, paymentStatus: 'paid', email: '<EMAIL>', phone: '****** 567 8905' },
                { id: 6, name: 'James Anderson', studentId: 'STU006', avatarColor: '#f44336', amountPaid: 600, amountRemaining: 900, paymentStatus: 'overdue', email: '<EMAIL>', phone: '****** 567 8906' },
                { id: 7, name: 'Maria Garcia', studentId: 'STU007', avatarColor: '#00bcd4', amountPaid: 1300, amountRemaining: 200, paymentStatus: 'pending', email: '<EMAIL>', phone: '****** 567 8907' },
                { id: 8, name: 'Robert Taylor', studentId: 'STU008', avatarColor: '#795548', amountPaid: 2000, amountRemaining: 0, paymentStatus: 'paid', email: '<EMAIL>', phone: '****** 567 8908' }
            ])
        ],

        // Helper function to generate basic student data
        generateBasicStudentData(basicStudents) {
            return basicStudents.map(student => ({
                ...student,
                photo: null,
                dateOfBirth: 'January 1, 2005',
                age: 18,
                gender: 'Not specified',
                address: '123 Main Street, Springfield, IL',
                guardian: 'Parent/Guardian',
                grade: '12th',
                overallGPA: '3.5',
                classRank: '50/120',
                attendanceRate: 90,
                totalFees: student.amountPaid + student.amountRemaining,
                lastPaymentDate: 'December 1, 2023',
                termResults: [
                    {
                        term: 'Fall 2023',
                        gpa: '3.5',
                        subjects: [
                            { name: 'Mathematics', grade: 'B' },
                            { name: 'English', grade: 'B+' },
                            { name: 'Science', grade: 'B' },
                            { name: 'History', grade: 'B-' }
                        ]
                    }
                ],
                attendanceStats: {
                    present: 135,
                    absent: 10,
                    late: 5,
                    excused: 2
                },
                emergencyContacts: [
                    {
                        name: 'Emergency Contact',
                        relationship: 'Parent',
                        phone: student.phone
                    }
                ]
            }));
        },

        // Computed property for filtered students
        get filteredStudents() {
            let filtered = this.students;
            
            // Apply search filter
            if (this.searchQuery.trim()) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(student => 
                    student.name.toLowerCase().includes(query) ||
                    student.studentId.toLowerCase().includes(query) ||
                    student.email.toLowerCase().includes(query)
                );
            }
            
            // Apply status filter
            if (this.filter !== 'all') {
                filtered = filtered.filter(student => student.paymentStatus === this.filter);
            }
            
            return filtered;
        },
        
        // Toggle drawer
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },

        // Close drawer
        closeDrawer() {
            this.drawerOpen = false;
        },

        // Toggle search visibility
        toggleSearch() {
            this.searchActive = !this.searchActive;
            if (!this.searchActive) {
                this.searchQuery = '';
            }
        },
        
        // Clear search
        clearSearch() {
            this.searchQuery = '';
        },
        
        // Set filter
        setFilter(filter) {
            this.filter = filter;
        },
        
        // Get status icon
        getStatusIcon(status) {
            switch (status) {
                case 'paid':
                    return 'check_circle';
                case 'pending':
                    return 'schedule';
                case 'overdue':
                    return 'error';
                default:
                    return 'help';
            }
        },
        
        // Select student and show bottom sheet
        selectStudent(student) {
            this.selectedStudent = student;
            this.bottomSheetActive = true;
        },
        
        // Close bottom sheet
        closeBottomSheet() {
            this.bottomSheetActive = false;
            setTimeout(() => {
                this.selectedStudent = null;
            }, 300);
        },
        
        // Edit student
        editStudent(student) {
            this.closeBottomSheet();
            alert(`Edit student: ${student.name}`);
            // Here you would typically navigate to an edit form or open a modal
        },
        
        // Delete student
        deleteStudent(student) {
            if (confirm(`Are you sure you want to delete ${student.name}?`)) {
                const index = this.students.findIndex(s => s.id === student.id);
                if (index > -1) {
                    this.students.splice(index, 1);
                }
                this.closeBottomSheet();
            }
        },
        
        // Show more options
        showMore(student) {
            this.selectStudent(student);
        },
        
        // Show add student
        showAddStudent() {
            alert('Add new student');
            // Here you would typically navigate to an add form or open a modal
        },
        
        // View details
        viewDetails() {
            this.selectedStudentDetail = this.selectedStudent;
            this.detailModalActive = true;
            this.closeBottomSheet();
        },

        // Open detail modal
        openDetailModal(student) {
            this.selectedStudentDetail = student;
            this.detailModalActive = true;
        },

        // Close detail modal
        closeDetailModal() {
            this.detailModalActive = false;
            setTimeout(() => {
                this.selectedStudentDetail = null;
            }, 300);
        },
        
        // Record payment
        recordPayment() {
            this.closeBottomSheet();
            alert(`Record payment for: ${this.selectedStudent.name}`);
            // Here you would typically open a payment form
        },
        
        // Send message
        sendMessage() {
            this.closeBottomSheet();
            alert(`Send message to: ${this.selectedStudent.name}`);
            // Here you would typically open a messaging interface
        },
        
        // Initialize component
        init() {
            // Add any initialization logic here
            console.log('Students list initialized');
        }
    };
}
