// Alpine.js Students List Component
function studentsList() {
    return {
        drawerOpen: false,
        searchActive: false,
        searchQuery: '',
        filter: 'all',
        bottomSheetActive: false,
        selectedStudent: null,
        
        // Sample student data
        students: [
            {
                id: 1,
                name: '<PERSON>',
                studentId: 'STU001',
                photo: null,
                avatarColor: '#e91e63',
                amountPaid: 1200,
                amountRemaining: 300,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8901'
            },
            {
                id: 2,
                name: '<PERSON>',
                studentId: 'STU002',
                photo: null,
                avatarColor: '#2196f3',
                amountPaid: 1500,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8902'
            },
            {
                id: 3,
                name: '<PERSON>',
                studentId: 'STU003',
                photo: null,
                avatarColor: '#4caf50',
                amountPaid: 800,
                amountRemaining: 700,
                paymentStatus: 'overdue',
                email: '<EMAIL>',
                phone: '****** 567 8903'
            },
            {
                id: 4,
                name: '<PERSON>',
                studentId: 'STU004',
                photo: null,
                avatarColor: '#ff9800',
                amountPaid: 1000,
                amountRemaining: 500,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8904'
            },
            {
                id: 5,
                name: 'Lisa Thompson',
                studentId: 'STU005',
                photo: null,
                avatarColor: '#9c27b0',
                amountPaid: 1800,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8905'
            },
            {
                id: 6,
                name: 'James Anderson',
                studentId: 'STU006',
                photo: null,
                avatarColor: '#f44336',
                amountPaid: 600,
                amountRemaining: 900,
                paymentStatus: 'overdue',
                email: '<EMAIL>',
                phone: '****** 567 8906'
            },
            {
                id: 7,
                name: 'Maria Garcia',
                studentId: 'STU007',
                photo: null,
                avatarColor: '#00bcd4',
                amountPaid: 1300,
                amountRemaining: 200,
                paymentStatus: 'pending',
                email: '<EMAIL>',
                phone: '****** 567 8907'
            },
            {
                id: 8,
                name: 'Robert Taylor',
                studentId: 'STU008',
                photo: null,
                avatarColor: '#795548',
                amountPaid: 2000,
                amountRemaining: 0,
                paymentStatus: 'paid',
                email: '<EMAIL>',
                phone: '****** 567 8908'
            }
        ],
        
        // Computed property for filtered students
        get filteredStudents() {
            let filtered = this.students;
            
            // Apply search filter
            if (this.searchQuery.trim()) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(student => 
                    student.name.toLowerCase().includes(query) ||
                    student.studentId.toLowerCase().includes(query) ||
                    student.email.toLowerCase().includes(query)
                );
            }
            
            // Apply status filter
            if (this.filter !== 'all') {
                filtered = filtered.filter(student => student.paymentStatus === this.filter);
            }
            
            return filtered;
        },
        
        // Toggle drawer
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },

        // Close drawer
        closeDrawer() {
            this.drawerOpen = false;
        },

        // Toggle search visibility
        toggleSearch() {
            this.searchActive = !this.searchActive;
            if (!this.searchActive) {
                this.searchQuery = '';
            }
        },
        
        // Clear search
        clearSearch() {
            this.searchQuery = '';
        },
        
        // Set filter
        setFilter(filter) {
            this.filter = filter;
        },
        
        // Get status icon
        getStatusIcon(status) {
            switch (status) {
                case 'paid':
                    return 'check_circle';
                case 'pending':
                    return 'schedule';
                case 'overdue':
                    return 'error';
                default:
                    return 'help';
            }
        },
        
        // Select student and show bottom sheet
        selectStudent(student) {
            this.selectedStudent = student;
            this.bottomSheetActive = true;
        },
        
        // Close bottom sheet
        closeBottomSheet() {
            this.bottomSheetActive = false;
            setTimeout(() => {
                this.selectedStudent = null;
            }, 300);
        },
        
        // Edit student
        editStudent(student) {
            this.closeBottomSheet();
            alert(`Edit student: ${student.name}`);
            // Here you would typically navigate to an edit form or open a modal
        },
        
        // Delete student
        deleteStudent(student) {
            if (confirm(`Are you sure you want to delete ${student.name}?`)) {
                const index = this.students.findIndex(s => s.id === student.id);
                if (index > -1) {
                    this.students.splice(index, 1);
                }
                this.closeBottomSheet();
            }
        },
        
        // Show more options
        showMore(student) {
            this.selectStudent(student);
        },
        
        // Show add student
        showAddStudent() {
            alert('Add new student');
            // Here you would typically navigate to an add form or open a modal
        },
        
        // View details
        viewDetails() {
            this.closeBottomSheet();
            alert(`View details for: ${this.selectedStudent.name}`);
            // Here you would typically navigate to a details page
        },
        
        // Record payment
        recordPayment() {
            this.closeBottomSheet();
            alert(`Record payment for: ${this.selectedStudent.name}`);
            // Here you would typically open a payment form
        },
        
        // Send message
        sendMessage() {
            this.closeBottomSheet();
            alert(`Send message to: ${this.selectedStudent.name}`);
            // Here you would typically open a messaging interface
        },
        
        // Initialize component
        init() {
            // Add any initialization logic here
            console.log('Students list initialized');
        }
    };
}
