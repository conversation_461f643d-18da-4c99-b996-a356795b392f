<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap 4.5 Scrollable Tabs</title>
    <!-- Bootstrap 4.5 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome for arrow icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        /* Custom styles for scrollable tabs */
        .nav-tabs-container {
            position: relative;
            overflow: hidden;
        }

        .nav-tabs-scroll {
            overflow-x: auto;
            overflow-y: hidden;
            display: flex;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .nav-tabs-scroll::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .nav-tabs {
            flex-wrap: nowrap;
            border-bottom: none;
            min-width: 100%;
        }
        
        .nav-tabs .nav-item {
            white-space: nowrap;
        }
        
        .scroll-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
            background-color: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .scroll-button:hover {
            background-color: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .scroll-button:focus {
            outline: none;
        }
        
        .scroll-button-left {
            left: 0;
        }
        
        .scroll-button-right {
            right: 0;
        }
        
        .scroll-button.disabled {
            opacity: 0.3;
            cursor: default;
        }
        
        /* Tab content styles */
        .tab-content {
            padding: 20px 0;
        }
        
        /* Hide scroll buttons on larger screens */
        @media (min-width: 992px) {
            .scroll-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="mb-4">Bootstrap 4.5 Scrollable Tabs</h2>
        
        <div class="nav-tabs-container">
            <!-- Left scroll button -->
            <button class="scroll-button scroll-button-left" id="scrollLeft">
                <i class="fas fa-chevron-left"></i>
            </button>
            
            <!-- Scrollable tabs container -->
            <div class="nav-tabs-scroll">
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Profile</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="contact-tab" data-toggle="tab" href="#contact" role="tab" aria-controls="contact" aria-selected="false">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="settings-tab" data-toggle="tab" href="#settings" role="tab" aria-controls="settings" aria-selected="false">Settings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="messages-tab" data-toggle="tab" href="#messages" role="tab" aria-controls="messages" aria-selected="false">Messages</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="projects-tab" data-toggle="tab" href="#projects" role="tab" aria-controls="projects" aria-selected="false">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="reports-tab" data-toggle="tab" href="#reports" role="tab" aria-controls="reports" aria-selected="false">Reports</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="analytics-tab" data-toggle="tab" href="#analytics" role="tab" aria-controls="analytics" aria-selected="false">Analytics</a>
                    </li>
                </ul>
            </div>
            
            <!-- Right scroll button -->
            <button class="scroll-button scroll-button-right" id="scrollRight">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
        
        <!-- Tab content -->
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                <h4>Home Content</h4>
                <p>This is the home tab content. Resize the window to see how the tabs become scrollable on smaller screens.</p>
            </div>
            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                <h4>Profile Content</h4>
                <p>This is the profile tab content.</p>
            </div>
            <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                <h4>Contact Content</h4>
                <p>This is the contact tab content.</p>
            </div>
            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                <h4>Settings Content</h4>
                <p>This is the settings tab content.</p>
            </div>
            <div class="tab-pane fade" id="messages" role="tabpanel" aria-labelledby="messages-tab">
                <h4>Messages Content</h4>
                <p>This is the messages tab content.</p>
            </div>
            <div class="tab-pane fade" id="projects" role="tabpanel" aria-labelledby="projects-tab">
                <h4>Projects Content</h4>
                <p>This is the projects tab content.</p>
            </div>
            <div class="tab-pane fade" id="reports" role="tabpanel" aria-labelledby="reports-tab">
                <h4>Reports Content</h4>
                <p>This is the reports tab content.</p>
            </div>
            <div class="tab-pane fade" id="analytics" role="tabpanel" aria-labelledby="analytics-tab">
                <h4>Analytics Content</h4>
                <p>This is the analytics tab content.</p>
            </div>
        </div>
    </div>

    <!-- Required JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
    <script>
        $(document).ready(function() {
            const scrollContainer = $('.nav-tabs-scroll');
            const scrollLeftBtn = $('#scrollLeft');
            const scrollRightBtn = $('#scrollRight');
            const scrollAmount = 100; // Pixels to scroll each time
            
            // Helper function to update button states
            function updateButtonStates() {
                // Check if we can scroll left
                if (scrollContainer.scrollLeft() <= 0) {
                    scrollLeftBtn.addClass('disabled');
                } else {
                    scrollLeftBtn.removeClass('disabled');
                }
                
                // Check if we can scroll right
                const maxScrollLeft = scrollContainer[0].scrollWidth - scrollContainer.outerWidth();
                if (scrollContainer.scrollLeft() >= maxScrollLeft - 5) { // 5px tolerance for rounding
                    scrollRightBtn.addClass('disabled');
                } else {
                    scrollRightBtn.removeClass('disabled');
                }
            }
            
            // Initial button state
            updateButtonStates();
            
            // Left scroll button click
            scrollLeftBtn.on('click', function() {
                if (!$(this).hasClass('disabled')) {
                    scrollContainer.animate({
                        scrollLeft: scrollContainer.scrollLeft() - scrollAmount
                    }, 200, updateButtonStates);
                }
            });
            
            // Right scroll button click
            scrollRightBtn.on('click', function() {
                if (!$(this).hasClass('disabled')) {
                    scrollContainer.animate({
                        scrollLeft: scrollContainer.scrollLeft() + scrollAmount
                    }, 200, updateButtonStates);
                }
            });
            
            // Update button states when scrolling manually
            scrollContainer.on('scroll', function() {
                updateButtonStates();
            });
            
            // Update button states on window resize
            $(window).on('resize', function() {
                updateButtonStates();
            });
            
            // Make the active tab visible when clicked
            $('.nav-link').on('click', function() {
                const $this = $(this);
                const $tabItem = $this.parent();
                const containerWidth = scrollContainer.width();
                const tabOffset = $tabItem.offset().left;
                const scrollLeft = scrollContainer.scrollLeft();
                const containerOffset = scrollContainer.offset().left;
                const tabWidth = $tabItem.width();
                
                // Calculate the target scroll position to center the tab
                const targetScrollLeft = scrollLeft + tabOffset - containerOffset - (containerWidth / 2) + (tabWidth / 2);
                
                // Animate scroll to make the active tab visible
                scrollContainer.animate({
                    scrollLeft: targetScrollLeft
                }, 200, updateButtonStates);
            });
        });
    </script>
</body>
</html>