<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - EduManager</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="list-styles.css">
    <link rel="stylesheet" href="dashboard-styles.css">
</head>
<body>
    <div x-data="dashboardApp()" class="app-container">
        <!-- Navigation Drawer -->
        <nav class="nav-drawer" :class="{'drawer-open': drawerOpen}">
            <div class="drawer-header">
                <div class="app-info">
                    <div class="app-icon">
                        <span class="material-icons">school</span>
                    </div>
                    <div class="app-details">
                        <h3>EduManager</h3>
                        <p>Student Management System</p>
                    </div>
                </div>
                <button class="close-drawer" @click="closeDrawer">
                    <span class="material-icons">close</span>
                </button>
            </div>
            
            <div class="drawer-content">
                <div class="nav-section">
                    <h4 class="nav-section-title">Main</h4>
                    <a href="dashboard.html" class="nav-item active">
                        <span class="material-icons">dashboard</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="list.html" class="nav-item">
                        <span class="material-icons">people</span>
                        <span class="nav-text">Students</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Academic</h4>
                    <a href="grades.html" class="nav-item">
                        <span class="material-icons">grade</span>
                        <span class="nav-text">Grades</span>
                    </a>
                    <a href="attendance.html" class="nav-item">
                        <span class="material-icons">event_available</span>
                        <span class="nav-text">Attendance</span>
                    </a>
                    <a href="courses.html" class="nav-item">
                        <span class="material-icons">book</span>
                        <span class="nav-text">Courses</span>
                    </a>
                    <a href="schedule.html" class="nav-item">
                        <span class="material-icons">schedule</span>
                        <span class="nav-text">Schedule</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Financial</h4>
                    <a href="payments.html" class="nav-item">
                        <span class="material-icons">payment</span>
                        <span class="nav-text">Payments</span>
                    </a>
                    <a href="fees.html" class="nav-item">
                        <span class="material-icons">account_balance</span>
                        <span class="nav-text">Fee Structure</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Communication</h4>
                    <a href="messages.html" class="nav-item">
                        <span class="material-icons">message</span>
                        <span class="nav-text">Messages</span>
                    </a>
                    <a href="notifications.html" class="nav-item">
                        <span class="material-icons">notifications</span>
                        <span class="nav-text">Notifications</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Reports</h4>
                    <a href="analytics.html" class="nav-item">
                        <span class="material-icons">analytics</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                    <a href="reports.html" class="nav-item">
                        <span class="material-icons">assessment</span>
                        <span class="nav-text">Reports</span>
                    </a>
                </div>
            </div>
            
            <div class="drawer-footer">
                <a href="settings.html" class="nav-item">
                    <span class="material-icons">settings</span>
                    <span class="nav-text">Settings</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <span class="material-icons">account_circle</span>
                    <span class="nav-text">Profile</span>
                </a>
            </div>
        </nav>
        
        <!-- Drawer Overlay -->
        <div class="drawer-overlay" :class="{'overlay-active': drawerOpen}" @click="closeDrawer"></div>

        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <button class="menu-btn" @click="toggleDrawer">
                    <span class="material-icons">menu</span>
                </button>
                <h1 class="page-title">Dashboard</h1>
                <div class="header-actions">
                    <button class="action-btn">
                        <span class="material-icons">notifications</span>
                    </button>
                    <button class="action-btn">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-icon students">
                        <span class="material-icons">people</span>
                    </div>
                    <div class="stat-info">
                        <h3>245</h3>
                        <p>Total Students</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon attendance">
                        <span class="material-icons">event_available</span>
                    </div>
                    <div class="stat-info">
                        <h3>92%</h3>
                        <p>Attendance Rate</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon grades">
                        <span class="material-icons">grade</span>
                    </div>
                    <div class="stat-info">
                        <h3>3.8</h3>
                        <p>Average GPA</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon payments">
                        <span class="material-icons">payment</span>
                    </div>
                    <div class="stat-info">
                        <h3>$45K</h3>
                        <p>Pending Fees</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2 class="section-title">Quick Actions</h2>
                <div class="action-grid">
                    <a href="list.html" class="action-card">
                        <div class="action-icon">
                            <span class="material-icons">people</span>
                        </div>
                        <h3>Manage Students</h3>
                        <p>View and manage student information</p>
                    </a>
                    <a href="attendance.html" class="action-card">
                        <div class="action-icon">
                            <span class="material-icons">event_available</span>
                        </div>
                        <h3>Take Attendance</h3>
                        <p>Mark student attendance for today</p>
                    </a>
                    <a href="grades.html" class="action-card">
                        <div class="action-icon">
                            <span class="material-icons">grade</span>
                        </div>
                        <h3>Enter Grades</h3>
                        <p>Add and manage student grades</p>
                    </a>
                    <a href="payments.html" class="action-card">
                        <div class="action-icon">
                            <span class="material-icons">payment</span>
                        </div>
                        <h3>Process Payments</h3>
                        <p>Handle fee payments and billing</p>
                    </a>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <h2 class="section-title">Recent Activity</h2>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">person_add</span>
                        </div>
                        <div class="activity-content">
                            <h4>New student enrolled</h4>
                            <p>Emma Johnson has been added to the system</p>
                            <span class="activity-time">2 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">grade</span>
                        </div>
                        <div class="activity-content">
                            <h4>Grades updated</h4>
                            <p>Mathematics test results have been published</p>
                            <span class="activity-time">4 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">payment</span>
                        </div>
                        <div class="activity-content">
                            <h4>Payment received</h4>
                            <p>Michael Chen paid $1,500 tuition fee</p>
                            <span class="activity-time">6 hours ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Custom JS -->
    <script src="dashboard-app.js"></script>
</body>
</html>
