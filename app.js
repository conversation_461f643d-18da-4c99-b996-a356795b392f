// Alpine.js Dashboard Component
function dashboard() {
    return {
        sidebarOpen: window.innerWidth >= 992,
        notificationsOpen: false,
        chatOpen: false,
        
        // Toggle sidebar visibility
        toggleSidebar() {
            this.sidebarOpen = !this.sidebarOpen;
        },
        
        // Toggle notifications panel
        toggleNotifications() {
            this.notificationsOpen = !this.notificationsOpen;
            if (this.notificationsOpen) {
                this.chatOpen = false;
            }
        },
        
        // Toggle chat panel
        toggleChat() {
            this.chatOpen = !this.chatOpen;
            if (this.chatOpen) {
                this.notificationsOpen = false;
            }
        },
        
        // Initialize charts when Alpine initializes
        init() {
            this.initRevenueChart();
            this.initPageViewsChart();
            this.initDownloadsChart();
            this.initSalesChart();
            this.initGrowthChart();
            this.initServerLoadChart();
            
            // Close panels when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.notification-bell') && 
                    !e.target.closest('.notification-panel') &&
                    this.notificationsOpen) {
                    this.notificationsOpen = false;
                }
                
                if (!e.target.closest('.chat-toggle') && 
                    !e.target.closest('.chat-panel') &&
                    this.chatOpen) {
                    this.chatOpen = false;
                }
            });
            
            // Handle responsive behavior
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 992) {
                    this.sidebarOpen = true;
                } else {
                    this.sidebarOpen = false;
                }
            });
        },
        
        // Revenue Chart
        initRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            
            // Generate random data
            const data = Array.from({length: 30}, () => Math.floor(Math.random() * 50) + 10);
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Array.from({length: 30}, (_, i) => i + 1),
                    datasets: [{
                        data: data,
                        backgroundColor: 'rgba(98, 0, 238, 0.2)',
                        borderColor: 'rgba(98, 0, 238, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false
                        }
                    }
                }
            });
        },
        
        // Page Views Chart
        initPageViewsChart() {
            const ctx = document.getElementById('pageViewsChart').getContext('2d');
            
            // Generate random data
            const data = Array.from({length: 30}, () => Math.floor(Math.random() * 50) + 10);
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Array.from({length: 30}, (_, i) => i + 1),
                    datasets: [{
                        data: data,
                        backgroundColor: 'rgba(66, 133, 244, 0.2)',
                        borderColor: 'rgba(66, 133, 244, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false
                        }
                    }
                }
            });
        },
        
        // Downloads Chart
        initDownloadsChart() {
            const ctx = document.getElementById('downloadsChart').getContext('2d');
            
            // Generate random data for multiple datasets
            const generateData = () => Array.from({length: 60}, () => Math.random() * 2);
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 60}, (_, i) => i + 1),
                    datasets: [
                        {
                            label: 'Product 1',
                            data: generateData(),
                            borderColor: 'rgba(66, 133, 244, 1)',
                            backgroundColor: 'rgba(66, 133, 244, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Product 2',
                            data: generateData(),
                            borderColor: 'rgba(156, 39, 176, 1)',
                            backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Product 3',
                            data: generateData(),
                            borderColor: 'rgba(52, 168, 83, 1)',
                            backgroundColor: 'rgba(52, 168, 83, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                maxTicksLimit: 5
                            }
                        },
                        y: {
                            display: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                maxTicksLimit: 5
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0
                        }
                    }
                }
            });
        },
        
        // Sales Chart
        initSalesChart() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            
            // Generate random data
            const data = Array.from({length: 30}, () => Math.floor(Math.random() * 50) + 10);
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 30}, (_, i) => i + 1),
                    datasets: [{
                        data: data,
                        borderColor: 'rgba(251, 188, 5, 1)',
                        backgroundColor: 'rgba(251, 188, 5, 0.1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false
                        }
                    },
                    elements: {
                        point: {
                            radius: 0
                        }
                    }
                }
            });
        },
        
        // Growth Chart
        initGrowthChart() {
            const ctx = document.getElementById('growthChart').getContext('2d');
            
            // Generate random data
            const data = Array.from({length: 30}, () => Math.floor(Math.random() * 50) + 10);
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Array.from({length: 30}, (_, i) => i + 1),
                    datasets: [{
                        data: data,
                        backgroundColor: 'rgba(52, 168, 83, 0.2)',
                        borderColor: 'rgba(52, 168, 83, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false
                        }
                    }
                }
            });
        },
        
        // Server Load Chart
        initServerLoadChart() {
            const ctx = document.getElementById('serverLoadChart').getContext('2d');
            
            // Generate random data
            const data = Array.from({length: 60}, () => Math.floor(Math.random() * 30) + 40);
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 60}, (_, i) => i + 1),
                    datasets: [{
                        data: data,
                        borderColor: 'rgba(98, 0, 238, 1)',
                        backgroundColor: 'rgba(98, 0, 238, 0.1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false
                        }
                    },
                    elements: {
                        point: {
                            radius: 0
                        }
                    }
                }
            });
        }
    };
}
