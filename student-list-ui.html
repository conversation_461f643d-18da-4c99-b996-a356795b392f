<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student List UI Designs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.5.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            background-color: #f5f6fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .section-divider {
            margin: 4rem 0;
            border-bottom: 3px solid #e9ecef;
            padding-bottom: 2rem;
        }

        .ui-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        /* UI 1: Card Grid Design */
        .ui-1 .student-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
        }

        .ui-1 .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .ui-1 .student-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #e9ecef;
            margin-bottom: 1rem;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .ui-1 .level-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .ui-1 .action-buttons {
            margin-top: 1rem;
        }

        .ui-1 .action-buttons .btn {
            margin: 0 0.25rem;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
        }

        /* UI 2: List View with Cards */
        .ui-2 .student-item {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
        }

        .ui-2 .student-item:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-left-color: var(--success-color);
        }

        .ui-2 .student-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            font-weight: bold;
            color: white;
        }

        .ui-2 .student-info h5 {
            margin-bottom: 0.25rem;
            color: var(--dark-color);
        }

        .ui-2 .student-meta {
            font-size: 0.875rem;
            color: var(--secondary-color);
        }

        .ui-2 .action-buttons .btn {
            padding: 0.375rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 6px;
        }

        /* UI 3: Material Design Cards */
        .ui-3 .student-card {
            background: white;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .ui-3 .student-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .ui-3 .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            position: relative;
        }

        .ui-3 .student-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            font-weight: bold;
            color: white;
        }

        .ui-3 .card-body {
            padding: 1.5rem;
        }

        .ui-3 .level-chip {
            background: var(--light-color);
            color: var(--dark-color);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .ui-3 .action-buttons {
            border-top: 1px solid #e9ecef;
            padding-top: 1rem;
        }

        .ui-3 .action-buttons .btn {
            margin: 0 0.25rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
        }

        /* UI 4: Modern Compact Design */
        .ui-4 .student-container {
            background: white;
            border-radius: 10px;
            margin-bottom: 1rem;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .ui-4 .student-container:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .ui-4 .student-header {
            background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
            height: 4px;
        }

        .ui-4 .student-content {
            padding: 1.25rem;
        }

        .ui-4 .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: bold;
            color: white;
        }

        .ui-4 .student-name {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.25rem;
        }

        .ui-4 .student-details {
            font-size: 0.875rem;
            color: var(--secondary-color);
        }

        .ui-4 .level-tag {
            background: var(--info-color);
            color: white;
            padding: 0.125rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .ui-4 .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        /* Gender Icons */
        .gender-icon {
            margin-right: 0.25rem;
        }

        .gender-male { color: #007bff; }
        .gender-female { color: #e83e8c; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .ui-1 .student-card {
                text-align: center;
            }
            
            .ui-2 .d-flex {
                flex-direction: column !important;
                text-align: center;
            }
            
            .ui-2 .student-avatar {
                align-self: center;
                margin-bottom: 1rem;
            }
            
            .action-buttons {
                text-align: center;
                margin-top: 1rem;
            }
        }

        @media (min-width: 992px) {
            .ui-1 .row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1.5rem;
            }
            
            .ui-1 .col-md-6 {
                max-width: none;
            }
        }

        /* Animation for action buttons */
        .btn {
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid px-3 px-md-5 py-4">
        
        <!-- UI Design 1: Card Grid -->
        <div class="ui-1">
            <div class="ui-title">
                <h2><i class="fas fa-graduation-cap mr-2"></i>UI Design 1: Card Grid Layout</h2>
                <p class="mb-0">Modern card-based design with hover effects</p>
            </div>
            
            <div class="row" id="students-ui-1">
                <!-- Students will be populated by JavaScript -->
            </div>
        </div>

        <div class="section-divider"></div>

        <!-- UI Design 2: List View -->
        <div class="ui-2">
            <div class="ui-title">
                <h2><i class="fas fa-list mr-2"></i>UI Design 2: Enhanced List View</h2>
                <p class="mb-0">Clean list design with left border accents</p>
            </div>
            
            <div id="students-ui-2">
                <!-- Students will be populated by JavaScript -->
            </div>
        </div>

        <div class="section-divider"></div>

        <!-- UI Design 3: Material Cards -->
        <div class="ui-3">
            <div class="ui-title">
                <h2><i class="fas fa-layer-group mr-2"></i>UI Design 3: Material Design Cards</h2>
                <p class="mb-0">Material design inspired with gradient headers</p>
            </div>
            
            <div class="row" id="students-ui-3">
                <!-- Students will be populated by JavaScript -->
            </div>
        </div>

        <div class="section-divider"></div>

        <!-- UI Design 4: Compact Modern -->
        <div class="ui-4">
            <div class="ui-title">
                <h2><i class="fas fa-stream mr-2"></i>UI Design 4: Compact Modern Design</h2>
                <p class="mb-0">Space-efficient design with colorful accents</p>
            </div>
            
            <div id="students-ui-4">
                <!-- Students will be populated by JavaScript -->
            </div>
        </div>

    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.5.3/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Sample student data
        const students = [
            {
                id: 1,
                name: "Emma Johnson",
                gender: "Female",
                level: "Senior",
                photo: "https://via.placeholder.com/150x150/FF69B4/FFFFFF?text=EJ"
            },
            {
                id: 2,
                name: "Michael Smith",
                gender: "Male",
                level: "Junior",
                photo: "https://via.placeholder.com/150x150/4169E1/FFFFFF?text=MS"
            },
            {
                id: 3,
                name: "Sofia Rodriguez",
                gender: "Female",
                level: "Sophomore",
                photo: "https://via.placeholder.com/150x150/FF1493/FFFFFF?text=SR"
            },
            {
                id: 4,
                name: "James Wilson",
                gender: "Male",
                level: "Freshman",
                photo: "https://via.placeholder.com/150x150/32CD32/FFFFFF?text=JW"
            },
            {
                id: 5,
                name: "Isabella Brown",
                gender: "Female",
                level: "Senior",
                photo: "https://via.placeholder.com/150x150/DA70D6/FFFFFF?text=IB"
            },
            {
                id: 6,
                name: "David Lee",
                gender: "Male",
                level: "Junior",
                photo: "https://via.placeholder.com/150x150/1E90FF/FFFFFF?text=DL"
            }
        ];

        // Get level badge color
        function getLevelColor(level) {
            const colors = {
                'Freshman': 'success',
                'Sophomore': 'info',
                'Junior': 'warning',
                'Senior': 'danger'
            };
            return colors[level] || 'secondary';
        }

        // Generate avatar image with initials and color
        function getAvatarImage(student) {
            const colors = {
                'Female': ['#FF69B4', '#FF1493', '#DA70D6', '#DDA0DD', '#FF6347'],
                'Male': ['#4169E1', '#32CD32', '#1E90FF', '#20B2AA', '#FF7F50']
            };
            const genderColors = colors[student.gender] || colors['Male'];
            const colorIndex = student.id % genderColors.length;
            const color = genderColors[colorIndex];
            const initials = student.name.split(' ').map(n => n[0]).join('');
            
            return `https://via.placeholder.com/150x150/${color.replace('#', '')}/FFFFFF?text=${initials}`;
        }

        // Get gender icon
        function getGenderIcon(gender) {
            return gender.toLowerCase() === 'male' ? 
                '<i class="fas fa-mars gender-icon gender-male"></i>' : 
                '<i class="fas fa-venus gender-icon gender-female"></i>';
        }
        function editStudent(id) {
            alert(`Edit student with ID: ${id}`);
        }

        function deleteStudent(id) {
            if (confirm(`Are you sure you want to delete student with ID: ${id}?`)) {
                alert(`Student with ID: ${id} deleted`);
            }
        }

        function viewDetails(id) {
            alert(`View details for student with ID: ${id}`);
        }

        // Action button handlers
        function generateUI1() {
            const container = document.getElementById('students-ui-1');
            container.innerHTML = students.map(student => `
                <div class="col-md-6 col-lg-4">
                    <div class="student-card position-relative">
                        <span class="level-badge badge badge-${getLevelColor(student.level)}">${student.level}</span>
                        <div class="text-center">
                            <img src="${getAvatarImage(student)}" alt="${student.name}" class="student-avatar">
                            <h5 class="mb-2">${student.name}</h5>
                            <p class="text-muted mb-3">
                                ${getGenderIcon(student.gender)}${student.gender}
                            </p>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewDetails(${student.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="editStudent(${student.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteStudent(${student.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Generate UI 2: List View
        function generateUI2() {
            const container = document.getElementById('students-ui-2');
            container.innerHTML = students.map(student => `
                <div class="student-item">
                    <div class="d-flex align-items-center">
                        <img src="${getAvatarImage(student)}" alt="${student.name}" class="student-avatar mr-3">
                        <div class="student-info flex-grow-1">
                            <h5 class="mb-1">${student.name}</h5>
                            <div class="student-meta">
                                ${getGenderIcon(student.gender)}${student.gender} • 
                                <span class="badge badge-${getLevelColor(student.level)} ml-1">${student.level}</span>
                            </div>
                        </div>
                        <div class="action-buttons ml-auto">
                            <button class="btn btn-sm btn-outline-info" onclick="viewDetails(${student.id})" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="editStudent(${student.id})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteStudent(${student.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Generate UI 3: Material Cards
        function generateUI3() {
            const container = document.getElementById('students-ui-3');
            container.innerHTML = students.map(student => `
                <div class="col-md-6 col-lg-4">
                    <div class="student-card">
                        <div class="card-header d-flex align-items-center">
                            <img src="${getAvatarImage(student)}" alt="${student.name}" class="student-avatar mr-3">
                            <div>
                                <h6 class="mb-1 text-white">${student.name}</h6>
                                <small class="text-white-50">${getGenderIcon(student.gender)}${student.gender}</small>
                            </div>
                        </div>
                        <div class="card-body">
                            <span class="level-chip">${student.level}</span>
                            <div class="action-buttons text-center">
                                <button class="btn btn-sm btn-primary" onclick="viewDetails(${student.id})">
                                    <i class="fas fa-eye mr-1"></i>Details
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="editStudent(${student.id})">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteStudent(${student.id})">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Generate UI 4: Compact Modern
        function generateUI4() {
            const container = document.getElementById('students-ui-4');
            container.innerHTML = students.map(student => `
                <div class="student-container">
                    <div class="student-header"></div>
                    <div class="student-content">
                        <div class="d-flex align-items-center">
                            <img src="${getAvatarImage(student)}" alt="${student.name}" class="student-avatar mr-3">
                            <div class="flex-grow-1">
                                <div class="student-name">${student.name}</div>
                                <div class="student-details">
                                    ${getGenderIcon(student.gender)}${student.gender} • 
                                    <span class="level-tag ml-1">${student.level}</span>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-secondary" onclick="viewDetails(${student.id})" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="editStudent(${student.id})" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="deleteStudent(${student.id})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Initialize all UIs
        $(document).ready(function() {
            generateUI1();
            generateUI2();
            generateUI3();
            generateUI4();
        });
    </script>
</body>
</html>