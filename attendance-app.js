// Alpine.js Attendance List Component
function attendanceList() {
    return {
        drawerOpen: false,
        searchActive: false,
        searchQuery: '',
        filter: 'all',
        bottomSheetActive: false,
        selectedRecord: null,
        currentDate: new Date(),
        
        // Sample attendance data
        attendance: [
            {
                id: 1,
                studentName: '<PERSON>',
                studentId: 'STU001',
                status: 'present',
                checkInTime: '08:15 AM',
                avatarColor: '#e91e63'
            },
            {
                id: 2,
                studentName: '<PERSON>',
                studentId: 'STU002',
                status: 'present',
                checkInTime: '08:10 AM',
                avatarColor: '#2196f3'
            },
            {
                id: 3,
                studentName: '<PERSON>',
                studentId: 'STU003',
                status: 'absent',
                checkInTime: null,
                avatarColor: '#4caf50'
            },
            {
                id: 4,
                studentName: '<PERSON>',
                studentId: 'STU004',
                status: 'late',
                checkInTime: '08:45 AM',
                avatarColor: '#ff9800'
            },
            {
                id: 5,
                studentName: '<PERSON>',
                studentId: 'STU005',
                status: 'present',
                checkInTime: '08:05 AM',
                avatarColor: '#9c27b0'
            },
            {
                id: 6,
                studentName: 'James <PERSON>',
                studentId: 'STU006',
                status: 'absent',
                checkInTime: null,
                avatarColor: '#f44336'
            },
            {
                id: 7,
                studentName: 'Maria Garcia',
                studentId: 'STU007',
                status: 'present',
                checkInTime: '08:20 AM',
                avatarColor: '#00bcd4'
            },
            {
                id: 8,
                studentName: 'Robert Taylor',
                studentId: 'STU008',
                status: 'late',
                checkInTime: '08:35 AM',
                avatarColor: '#795548'
            }
        ],
        
        // Computed property for filtered attendance
        get filteredAttendance() {
            let filtered = this.attendance;
            
            // Apply search filter
            if (this.searchQuery.trim()) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(record => 
                    record.studentName.toLowerCase().includes(query) ||
                    record.studentId.toLowerCase().includes(query)
                );
            }
            
            // Apply status filter
            if (this.filter !== 'all') {
                filtered = filtered.filter(record => record.status === this.filter);
            }
            
            return filtered;
        },
        
        // Toggle drawer
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },
        
        // Close drawer
        closeDrawer() {
            this.drawerOpen = false;
        },
        
        // Toggle search visibility
        toggleSearch() {
            this.searchActive = !this.searchActive;
            if (!this.searchActive) {
                this.searchQuery = '';
            }
        },
        
        // Clear search
        clearSearch() {
            this.searchQuery = '';
        },
        
        // Set filter
        setFilter(filter) {
            this.filter = filter;
        },
        
        // Date navigation
        previousDate() {
            this.currentDate = new Date(this.currentDate.getTime() - 24 * 60 * 60 * 1000);
        },
        
        nextDate() {
            this.currentDate = new Date(this.currentDate.getTime() + 24 * 60 * 60 * 1000);
        },
        
        // Format date
        formatDate(date) {
            return date.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        },
        
        // Get day of week
        getDayOfWeek(date) {
            return date.toLocaleDateString('en-US', { weekday: 'long' });
        },
        
        // Get status icon
        getStatusIcon(status) {
            switch (status) {
                case 'present':
                    return 'check_circle';
                case 'absent':
                    return 'cancel';
                case 'late':
                    return 'schedule';
                case 'excused':
                    return 'event_busy';
                default:
                    return 'help';
            }
        },
        
        // Get attendance counts
        getPresentCount() {
            return this.attendance.filter(record => record.status === 'present').length;
        },
        
        getAbsentCount() {
            return this.attendance.filter(record => record.status === 'absent').length;
        },
        
        getLateCount() {
            return this.attendance.filter(record => record.status === 'late').length;
        },
        
        getTotalCount() {
            return this.attendance.length;
        },
        
        // Mark attendance
        markPresent(record) {
            record.status = 'present';
            record.checkInTime = new Date().toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            this.closeBottomSheet();
        },
        
        markAbsent(record) {
            record.status = 'absent';
            record.checkInTime = null;
            this.closeBottomSheet();
        },
        
        markLate(record) {
            record.status = 'late';
            record.checkInTime = new Date().toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            this.closeBottomSheet();
        },
        
        // Mark all present
        markAllPresent() {
            if (confirm('Mark all students as present?')) {
                this.attendance.forEach(record => {
                    if (record.status !== 'present') {
                        record.status = 'present';
                        record.checkInTime = new Date().toLocaleTimeString('en-US', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                        });
                    }
                });
            }
        },
        
        // Select record and show bottom sheet
        selectRecord(record) {
            this.selectedRecord = record;
            this.bottomSheetActive = true;
        },
        
        // Close bottom sheet
        closeBottomSheet() {
            this.bottomSheetActive = false;
            setTimeout(() => {
                this.selectedRecord = null;
            }, 300);
        },
        
        // Show more options
        showMore(record) {
            this.selectRecord(record);
        },
        
        // Show date picker
        showDatePicker() {
            alert('Date picker functionality');
            // Here you would typically open a date picker
        },
        
        // View history
        viewHistory() {
            this.closeBottomSheet();
            alert(`View attendance history for: ${this.selectedRecord.studentName}`);
            // Here you would typically navigate to a history page
        },
        
        // Send notification
        sendNotification() {
            this.closeBottomSheet();
            alert(`Send notification to: ${this.selectedRecord.studentName}`);
            // Here you would typically send a notification
        },
        
        // Initialize component
        init() {
            console.log('Attendance list initialized');
        }
    };
}
