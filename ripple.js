// Material Design Ripple Effects JavaScript
class MaterialRipple {
    constructor() {
        this.init();
    }

    init() {
        // Auto-initialize ripples on page load
        document.addEventListener('DOMContentLoaded', () => {
            this.addRippleToElements();
        });

        // Re-initialize ripples for dynamically added elements
        this.observeNewElements();
    }

    // Add ripple effect to all eligible elements
    addRippleToElements() {
        const rippleSelectors = [
            '.student-card',
            '.grade-card', 
            '.attendance-card',
            '.action-card',
            '.performance-card',
            '.summary-card',
            '.stat-card',
            '.term-card',
            '.filter-chip',
            '.nav-item',
            '.action-btn',
            '.action-btn-small',
            '.btn',
            '.btn-primary',
            '.btn-text',
            '.fab',
            '.sheet-action',
            '.contact-action',
            '.attendance-stat',
            '.date-nav-btn',
            '.clear-search',
            '.close-btn',
            '.close-drawer',
            '.back-btn',
            '.edit-btn',
            '.menu-btn'
        ];

        rippleSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (!element.classList.contains('ripple')) {
                    this.addRipple(element);
                }
            });
        });
    }

    // Add ripple class and event listeners to an element
    addRipple(element) {
        element.classList.add('ripple');
        
        // Add click event for enhanced ripple effect
        element.addEventListener('click', (e) => {
            this.createRippleEffect(e, element);
        });

        // Add touch events for mobile
        element.addEventListener('touchstart', (e) => {
            this.createRippleEffect(e.touches[0], element);
        });
    }

    // Create enhanced ripple effect at click/touch position
    createRippleEffect(event, element) {
        // Remove existing ripple effects
        const existingRipples = element.querySelectorAll('.ripple-effect');
        existingRipples.forEach(ripple => ripple.remove());

        // Get element dimensions and position
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        // Create ripple element
        const ripple = document.createElement('span');
        ripple.classList.add('ripple-effect');
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';

        // Set ripple color based on element background
        const computedStyle = window.getComputedStyle(element);
        const backgroundColor = computedStyle.backgroundColor;
        
        if (this.isLightColor(backgroundColor)) {
            ripple.style.color = 'rgba(0, 0, 0, 0.12)';
        } else {
            ripple.style.color = 'rgba(255, 255, 255, 0.2)';
        }

        // Add ripple to element
        element.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    // Determine if a color is light or dark
    isLightColor(color) {
        // Convert color to RGB values
        const rgb = color.match(/\d+/g);
        if (!rgb || rgb.length < 3) return true;

        // Calculate luminance
        const luminance = (0.299 * rgb[0] + 0.587 * rgb[1] + 0.114 * rgb[2]) / 255;
        return luminance > 0.5;
    }

    // Observe for new elements added to the DOM
    observeNewElements() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // Check if the added node itself needs ripple
                        this.checkAndAddRipple(node);
                        
                        // Check child elements
                        const rippleElements = node.querySelectorAll ? 
                            node.querySelectorAll('[class*="card"], [class*="btn"], [class*="action"]') : [];
                        rippleElements.forEach(element => {
                            this.checkAndAddRipple(element);
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Check if element needs ripple and add it
    checkAndAddRipple(element) {
        const rippleClasses = [
            'student-card', 'grade-card', 'attendance-card', 'action-card',
            'performance-card', 'summary-card', 'stat-card', 'term-card',
            'filter-chip', 'nav-item', 'action-btn', 'action-btn-small',
            'btn', 'btn-primary', 'btn-text', 'fab', 'sheet-action',
            'contact-action', 'attendance-stat', 'date-nav-btn',
            'clear-search', 'close-btn', 'close-drawer', 'back-btn',
            'edit-btn', 'menu-btn'
        ];

        const hasRippleClass = rippleClasses.some(className => 
            element.classList.contains(className)
        );

        if (hasRippleClass && !element.classList.contains('ripple')) {
            this.addRipple(element);
        }
    }

    // Public method to manually add ripple to specific elements
    static addTo(selector) {
        const elements = document.querySelectorAll(selector);
        const rippleInstance = new MaterialRipple();
        
        elements.forEach(element => {
            rippleInstance.addRipple(element);
        });
    }

    // Public method to remove ripple from elements
    static removeFrom(selector) {
        const elements = document.querySelectorAll(selector);
        
        elements.forEach(element => {
            element.classList.remove('ripple');
            const rippleEffects = element.querySelectorAll('.ripple-effect');
            rippleEffects.forEach(ripple => ripple.remove());
        });
    }

    // Public method to create instant ripple effect
    static trigger(element, x = null, y = null) {
        const rippleInstance = new MaterialRipple();
        
        // If no coordinates provided, use center of element
        if (x === null || y === null) {
            const rect = element.getBoundingClientRect();
            x = rect.width / 2;
            y = rect.height / 2;
        }

        const fakeEvent = {
            clientX: element.getBoundingClientRect().left + x,
            clientY: element.getBoundingClientRect().top + y
        };

        rippleInstance.createRippleEffect(fakeEvent, element);
    }
}

// Initialize ripple effects
const materialRipple = new MaterialRipple();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MaterialRipple;
}

// Global access
window.MaterialRipple = MaterialRipple;
