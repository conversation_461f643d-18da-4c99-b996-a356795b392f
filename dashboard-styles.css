/* Dashboard Specific Styles */

.dashboard-content {
    padding: 16px;
    padding-bottom: 80px;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    margin-bottom: 24px;
}

.stat-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 16px;
    box-shadow: var(--elevation-1);
    display: flex;
    align-items: center;
    gap: 12px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
}

.stat-icon.students {
    background-color: #2196f3;
}

.stat-icon.attendance {
    background-color: #4caf50;
}

.stat-icon.grades {
    background-color: #ff9800;
}

.stat-icon.payments {
    background-color: #e91e63;
}

.stat-info h3 {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.stat-info p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Section Titles */
.section-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 16px;
    color: var(--text-primary);
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 24px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.action-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--elevation-1);
    text-decoration: none;
    color: inherit;
    transition: all 0.2s;
    display: block;
}

.action-card:hover {
    box-shadow: var(--elevation-2);
    transform: translateY(-2px);
    color: inherit;
    text-decoration: none;
}

.action-card:active {
    transform: translateY(0);
    box-shadow: var(--elevation-1);
}

.action-icon {
    width: 56px;
    height: 56px;
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 28px;
}

.action-card h3 {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px;
    color: var(--text-primary);
}

.action-card p {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* Recent Activity */
.recent-activity {
    margin-bottom: 24px;
}

.activity-list {
    background-color: var(--surface-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--elevation-1);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px;
    color: var(--text-primary);
}

.activity-content p {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0 0 4px;
    line-height: 1.4;
}

.activity-time {
    font-size: 12px;
    color: var(--text-disabled);
}

/* Dashboard Charts */
.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.chart-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--elevation-1);
}

.chart-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 16px;
    color: var(--text-primary);
}

.chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 14px;
}

/* Upcoming Events */
.upcoming-events {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--elevation-1);
    margin-bottom: 24px;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.event-item:last-child {
    border-bottom: none;
}

.event-date {
    width: 48px;
    height: 48px;
    background-color: var(--primary-color);
    color: var(--text-light);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.event-day {
    font-size: 16px;
    font-weight: 700;
    line-height: 1;
}

.event-month {
    font-size: 10px;
    text-transform: uppercase;
    line-height: 1;
}

.event-details h4 {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px;
    color: var(--text-primary);
}

.event-details p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Responsive Design for Dashboard */
@media (max-width: 768px) {
    .dashboard-content {
        padding: 12px;
        padding-bottom: 80px;
    }
    
    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        padding: 12px;
        gap: 8px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
    }
    
    .stat-info h3 {
        font-size: 20px;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .action-card {
        padding: 16px;
    }
    
    .action-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
        margin-bottom: 12px;
    }
    
    .activity-item {
        padding: 12px;
        gap: 12px;
    }
    
    .activity-icon {
        width: 36px;
        height: 36px;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .chart-card {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 16px;
        justify-content: center;
        text-align: center;
        flex-direction: column;
        gap: 8px;
    }
    
    .section-title {
        font-size: 16px;
    }
    
    .action-card h3 {
        font-size: 15px;
    }
    
    .action-card p {
        font-size: 13px;
    }
}
