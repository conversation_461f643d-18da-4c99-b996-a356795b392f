<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students List - Material Design</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="list-styles.css">
</head>
<body>
    <div x-data="studentsList()" class="app-container">
        <!-- Navigation Drawer -->
        <nav class="nav-drawer" :class="{'drawer-open': drawerOpen}">
            <div class="drawer-header">
                <div class="app-info">
                    <div class="app-icon">
                        <span class="material-icons">school</span>
                    </div>
                    <div class="app-details">
                        <h3>EduManager</h3>
                        <p>Student Management System</p>
                    </div>
                </div>
                <button class="close-drawer" @click="closeDrawer">
                    <span class="material-icons">close</span>
                </button>
            </div>

            <div class="drawer-content">
                <div class="nav-section">
                    <h4 class="nav-section-title">Main</h4>
                    <a href="dashboard.html" class="nav-item">
                        <span class="material-icons">dashboard</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="list.html" class="nav-item active">
                        <span class="material-icons">people</span>
                        <span class="nav-text">Students</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Academic</h4>
                    <a href="grades.html" class="nav-item">
                        <span class="material-icons">grade</span>
                        <span class="nav-text">Grades</span>
                    </a>
                    <a href="attendance.html" class="nav-item">
                        <span class="material-icons">event_available</span>
                        <span class="nav-text">Attendance</span>
                    </a>
                    <a href="courses.html" class="nav-item">
                        <span class="material-icons">book</span>
                        <span class="nav-text">Courses</span>
                    </a>
                    <a href="schedule.html" class="nav-item">
                        <span class="material-icons">schedule</span>
                        <span class="nav-text">Schedule</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Financial</h4>
                    <a href="payments.html" class="nav-item">
                        <span class="material-icons">payment</span>
                        <span class="nav-text">Payments</span>
                    </a>
                    <a href="fees.html" class="nav-item">
                        <span class="material-icons">account_balance</span>
                        <span class="nav-text">Fee Structure</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Communication</h4>
                    <a href="messages.html" class="nav-item">
                        <span class="material-icons">message</span>
                        <span class="nav-text">Messages</span>
                    </a>
                    <a href="notifications.html" class="nav-item">
                        <span class="material-icons">notifications</span>
                        <span class="nav-text">Notifications</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Reports</h4>
                    <a href="analytics.html" class="nav-item">
                        <span class="material-icons">analytics</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                    <a href="reports.html" class="nav-item">
                        <span class="material-icons">assessment</span>
                        <span class="nav-text">Reports</span>
                    </a>
                </div>
            </div>

            <div class="drawer-footer">
                <a href="settings.html" class="nav-item">
                    <span class="material-icons">settings</span>
                    <span class="nav-text">Settings</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <span class="material-icons">account_circle</span>
                    <span class="nav-text">Profile</span>
                </a>
            </div>
        </nav>

        <!-- Drawer Overlay -->
        <div class="drawer-overlay" :class="{'overlay-active': drawerOpen}" @click="closeDrawer"></div>
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <button class="menu-btn" @click="toggleDrawer">
                    <span class="material-icons">menu</span>
                </button>
                <h1 class="page-title">Students</h1>
                <div class="header-actions">
                    <button class="action-btn" @click="toggleSearch">
                        <span class="material-icons">search</span>
                    </button>
                    <button class="action-btn" @click="showAddStudent">
                        <span class="material-icons">add</span>
                    </button>
                    <button class="action-btn">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="search-container" :class="{'search-active': searchActive}">
                <div class="search-box">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" placeholder="Search students..." class="search-input" x-model="searchQuery">
                    <button class="clear-search" @click="clearSearch" x-show="searchQuery">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Filter Chips -->
        <div class="filter-container">
            <div class="filter-chips">
                <button class="filter-chip" :class="{'active': filter === 'all'}" @click="setFilter('all')">
                    All Students
                </button>
                <button class="filter-chip" :class="{'active': filter === 'paid'}" @click="setFilter('paid')">
                    Fully Paid
                </button>
                <button class="filter-chip" :class="{'active': filter === 'pending'}" @click="setFilter('pending')">
                    Pending Payment
                </button>
                <button class="filter-chip" :class="{'active': filter === 'overdue'}" @click="setFilter('overdue')">
                    Overdue
                </button>
            </div>
        </div>

        <!-- Students List -->
        <div class="students-container">
            <template x-for="student in filteredStudents" :key="student.id">
                <div class="student-card" @click="selectStudent(student)">
                    <div class="student-media">
                        <div class="student-avatar" :style="'background-color: ' + student.avatarColor">
                            <span class="material-icons" x-show="!student.photo">person</span>
                            <img x-show="student.photo" :src="student.photo" :alt="student.name">
                        </div>
                        <div class="student-status" :class="student.paymentStatus">
                            <span class="material-icons" x-text="getStatusIcon(student.paymentStatus)"></span>
                        </div>
                    </div>

                    <div class="student-content">
                        <div class="student-info">
                            <h3 class="student-name" x-text="student.name"></h3>
                            <p class="student-id" x-text="'ID: ' + student.studentId"></p>
                        </div>

                        <div class="payment-info">
                            <div class="payment-row">
                                <span class="payment-label">Paid:</span>
                                <span class="payment-amount paid" x-text="'$' + student.amountPaid"></span>
                            </div>
                            <div class="payment-row">
                                <span class="payment-label">Remaining:</span>
                                <span class="payment-amount remaining" x-text="'$' + student.amountRemaining"></span>
                            </div>
                        </div>
                    </div>

                    <div class="student-actions">
                        <button class="action-btn-small" @click.stop="editStudent(student)" title="Edit">
                            <span class="material-icons">edit</span>
                        </button>
                        <!-- <button class="action-btn-small" @click.stop="deleteStudent(student)" title="Delete">
                            <span class="material-icons">delete</span>
                        </button> -->
                        <button class="action-btn-small" @click.stop="showMore(student)" title="More">
                            <span class="material-icons">more_horiz</span>
                        </button>
                    </div>
                </div>
            </template>

            <!-- Empty State -->
            <div class="empty-state" x-show="filteredStudents.length === 0">
                <div class="empty-icon">
                    <span class="material-icons">school</span>
                </div>
                <h3>No students found</h3>
                <p>Try adjusting your search or filter criteria</p>
                <button class="btn-primary" @click="showAddStudent">
                    <span class="material-icons">add</span>
                    Add Student
                </button>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="fab" @click="showAddStudent" title="Add Student">
            <span class="material-icons">add</span>
        </button>

        <!-- Bottom Sheet for Actions -->
        <div class="bottom-sheet" :class="{'active': bottomSheetActive}" @click="closeBottomSheet">
            <div class="bottom-sheet-content" @click.stop>
                <div class="bottom-sheet-header">
                    <h3 x-text="selectedStudent?.name"></h3>
                    <button class="close-btn" @click="closeBottomSheet">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="bottom-sheet-actions">
                    <button class="sheet-action" @click="viewDetails">
                        <span class="material-icons">visibility</span>
                        View Details
                    </button>
                    <button class="sheet-action" @click="editStudent(selectedStudent)">
                        <span class="material-icons">edit</span>
                        Edit Student
                    </button>
                    <button class="sheet-action" @click="recordPayment">
                        <span class="material-icons">payment</span>
                        Record Payment
                    </button>
                    <button class="sheet-action" @click="sendMessage">
                        <span class="material-icons">message</span>
                        Send Message
                    </button>
                    <button class="sheet-action danger" @click="deleteStudent(selectedStudent)">
                        <span class="material-icons">delete</span>
                        Delete Student
                    </button>
                </div>
            </div>
        </div>

        <!-- Overlay -->
        <div class="overlay" :class="{'active': bottomSheetActive}" @click="closeBottomSheet"></div>

        <!-- Student Detail Modal -->
        <div class="student-detail-modal" :class="{'modal-active': detailModalActive}">
            <div class="detail-header">
                <button class="back-btn" @click="closeDetailModal">
                    <span class="material-icons">arrow_back</span>
                </button>
                <h2 class="detail-title">Student Details</h2>
                <button class="edit-btn" @click="editStudent(selectedStudentDetail)">
                    <span class="material-icons">edit</span>
                </button>
            </div>

            <div class="detail-content" x-show="selectedStudentDetail">
                <!-- Student Profile Section -->
                <div class="profile-section">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <div class="student-avatar large" :style="'background-color: ' + selectedStudentDetail?.avatarColor">
                                <span class="material-icons" x-show="!selectedStudentDetail?.photo">person</span>
                                <img x-show="selectedStudentDetail?.photo" :src="selectedStudentDetail?.photo" :alt="selectedStudentDetail?.name">
                            </div>
                            <div class="profile-status" :class="selectedStudentDetail?.paymentStatus">
                                <span class="material-icons" x-text="getStatusIcon(selectedStudentDetail?.paymentStatus)"></span>
                            </div>
                        </div>
                        <div class="profile-info">
                            <h1 class="student-name" x-text="selectedStudentDetail?.name"></h1>
                            <p class="student-id" x-text="'ID: ' + selectedStudentDetail?.studentId"></p>
                            <div class="profile-badges">
                                <span class="badge grade-badge" x-text="'Grade ' + selectedStudentDetail?.grade"></span>
                                <span class="badge status-badge" :class="selectedStudentDetail?.paymentStatus" x-text="selectedStudentDetail?.paymentStatus"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">person</span>
                        Personal Information
                    </h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Full Name</span>
                            <span class="info-value" x-text="selectedStudentDetail?.name"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date of Birth</span>
                            <span class="info-value" x-text="selectedStudentDetail?.dateOfBirth"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Age</span>
                            <span class="info-value" x-text="selectedStudentDetail?.age + ' years'"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Gender</span>
                            <span class="info-value" x-text="selectedStudentDetail?.gender"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Email</span>
                            <span class="info-value" x-text="selectedStudentDetail?.email"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Phone</span>
                            <span class="info-value" x-text="selectedStudentDetail?.phone"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Address</span>
                            <span class="info-value" x-text="selectedStudentDetail?.address"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Guardian</span>
                            <span class="info-value" x-text="selectedStudentDetail?.guardian"></span>
                        </div>
                    </div>
                </div>

                <!-- Academic Performance -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">school</span>
                        Academic Performance
                    </h3>
                    <div class="performance-summary">
                        <div class="performance-card">
                            <div class="performance-icon">
                                <span class="material-icons">grade</span>
                            </div>
                            <div class="performance-info">
                                <h4 x-text="selectedStudentDetail?.overallGPA"></h4>
                                <p>Overall GPA</p>
                            </div>
                        </div>
                        <div class="performance-card">
                            <div class="performance-icon">
                                <span class="material-icons">trending_up</span>
                            </div>
                            <div class="performance-info">
                                <h4 x-text="selectedStudentDetail?.classRank"></h4>
                                <p>Class Rank</p>
                            </div>
                        </div>
                        <div class="performance-card">
                            <div class="performance-icon">
                                <span class="material-icons">event_available</span>
                            </div>
                            <div class="performance-info">
                                <h4 x-text="selectedStudentDetail?.attendanceRate + '%'"></h4>
                                <p>Attendance</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Term Results -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">assessment</span>
                        Term Results Summary
                    </h3>
                    <div class="terms-container">
                        <template x-for="term in selectedStudentDetail?.termResults" :key="term.term">
                            <div class="term-card">
                                <div class="term-header">
                                    <h4 x-text="term.term"></h4>
                                    <span class="term-gpa" x-text="term.gpa"></span>
                                </div>
                                <div class="subjects-list">
                                    <template x-for="subject in term.subjects" :key="subject.name">
                                        <div class="subject-item">
                                            <span class="subject-name" x-text="subject.name"></span>
                                            <span class="subject-grade" :class="getGradeClass(subject.grade)" x-text="subject.grade"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Attendance Summary -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">event_available</span>
                        Attendance Summary
                    </h3>
                    <div class="attendance-stats">
                        <div class="attendance-stat">
                            <div class="stat-circle present">
                                <span x-text="selectedStudentDetail?.attendanceStats?.present"></span>
                            </div>
                            <p>Present</p>
                        </div>
                        <div class="attendance-stat">
                            <div class="stat-circle absent">
                                <span x-text="selectedStudentDetail?.attendanceStats?.absent"></span>
                            </div>
                            <p>Absent</p>
                        </div>
                        <div class="attendance-stat">
                            <div class="stat-circle late">
                                <span x-text="selectedStudentDetail?.attendanceStats?.late"></span>
                            </div>
                            <p>Late</p>
                        </div>
                        <div class="attendance-stat">
                            <div class="stat-circle excused">
                                <span x-text="selectedStudentDetail?.attendanceStats?.excused"></span>
                            </div>
                            <p>Excused</p>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">payment</span>
                        Payment Information
                    </h3>
                    <div class="payment-summary">
                        <div class="payment-item">
                            <span class="payment-label">Total Fees</span>
                            <span class="payment-amount total" x-text="'$' + selectedStudentDetail?.totalFees"></span>
                        </div>
                        <div class="payment-item">
                            <span class="payment-label">Amount Paid</span>
                            <span class="payment-amount paid" x-text="'$' + selectedStudentDetail?.amountPaid"></span>
                        </div>
                        <div class="payment-item">
                            <span class="payment-label">Amount Remaining</span>
                            <span class="payment-amount remaining" x-text="'$' + selectedStudentDetail?.amountRemaining"></span>
                        </div>
                        <div class="payment-item">
                            <span class="payment-label">Last Payment</span>
                            <span class="payment-date" x-text="selectedStudentDetail?.lastPaymentDate"></span>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">contact_phone</span>
                        Emergency Contacts
                    </h3>
                    <div class="contacts-list">
                        <template x-for="contact in selectedStudentDetail?.emergencyContacts" :key="contact.name">
                            <div class="contact-item">
                                <div class="contact-avatar">
                                    <span class="material-icons">person</span>
                                </div>
                                <div class="contact-info">
                                    <h4 x-text="contact.name"></h4>
                                    <p x-text="contact.relationship"></p>
                                    <p x-text="contact.phone"></p>
                                </div>
                                <button class="contact-action">
                                    <span class="material-icons">call</span>
                                </button>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Custom JS -->
    <script src="list-app.js"></script>
</body>
</html>