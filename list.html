<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students List - Material Design</title>

    <!-- Material Design Components CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="material-design.css">
    <link rel="stylesheet" href="ripple.css">
</head>
<body>
    <div x-data="studentsList()" class="app-container">
        <!-- Material Design Navigation Drawer -->
        <aside class="mdc-drawer mdc-drawer--modal" :class="{'mdc-drawer--open': drawerOpen}">
            <div class="mdc-drawer__header">
                <div class="app-info">
                    <div class="app-icon">
                        <span class="material-icons">school</span>
                    </div>
                    <div class="app-details">
                        <h3>EduManager</h3>
                        <p>Student Management System</p>
                    </div>
                </div>
                <button class="mdc-icon-button" @click="closeDrawer">
                    <span class="material-icons mdc-icon-button__icon">close</span>
                </button>
            </div>

            <div class="mdc-drawer__content">
                <div class="nav-section">
                    <h4 class="nav-section-title">Main</h4>
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="dashboard.html">
                            <span class="mdc-list-item__graphic material-icons">dashboard</span>
                            <span class="mdc-list-item__text">Dashboard</span>
                        </a>
                        <a class="mdc-list-item mdc-list-item--activated" href="list.html">
                            <span class="mdc-list-item__graphic material-icons">people</span>
                            <span class="mdc-list-item__text">Students</span>
                        </a>
                    </nav>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Academic</h4>
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="grades.html">
                            <span class="mdc-list-item__graphic material-icons">grade</span>
                            <span class="mdc-list-item__text">Grades</span>
                        </a>
                        <a class="mdc-list-item" href="attendance.html">
                            <span class="mdc-list-item__graphic material-icons">event_available</span>
                            <span class="mdc-list-item__text">Attendance</span>
                        </a>
                        <a class="mdc-list-item" href="courses.html">
                            <span class="mdc-list-item__graphic material-icons">book</span>
                            <span class="mdc-list-item__text">Courses</span>
                        </a>
                        <a class="mdc-list-item" href="schedule.html">
                            <span class="mdc-list-item__graphic material-icons">schedule</span>
                            <span class="mdc-list-item__text">Schedule</span>
                        </a>
                    </nav>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Financial</h4>
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="payments.html">
                            <span class="mdc-list-item__graphic material-icons">payment</span>
                            <span class="mdc-list-item__text">Payments</span>
                        </a>
                        <a class="mdc-list-item" href="fees.html">
                            <span class="mdc-list-item__graphic material-icons">account_balance</span>
                            <span class="mdc-list-item__text">Fee Structure</span>
                        </a>
                    </nav>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Communication</h4>
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="messages.html">
                            <span class="mdc-list-item__graphic material-icons">message</span>
                            <span class="mdc-list-item__text">Messages</span>
                        </a>
                        <a class="mdc-list-item" href="notifications.html">
                            <span class="mdc-list-item__graphic material-icons">notifications</span>
                            <span class="mdc-list-item__text">Notifications</span>
                        </a>
                    </nav>
                </div>

                <div class="nav-section">
                    <h4 class="nav-section-title">Reports</h4>
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="analytics.html">
                            <span class="mdc-list-item__graphic material-icons">analytics</span>
                            <span class="mdc-list-item__text">Analytics</span>
                        </a>
                        <a class="mdc-list-item" href="reports.html">
                            <span class="mdc-list-item__graphic material-icons">assessment</span>
                            <span class="mdc-list-item__text">Reports</span>
                        </a>
                    </nav>
                </div>
            </div>

            <div class="mdc-drawer__footer">
                <nav class="mdc-list">
                    <a class="mdc-list-item" href="settings.html">
                        <span class="mdc-list-item__graphic material-icons">settings</span>
                        <span class="mdc-list-item__text">Settings</span>
                    </a>
                    <a class="mdc-list-item" href="profile.html">
                        <span class="mdc-list-item__graphic material-icons">account_circle</span>
                        <span class="mdc-list-item__text">Profile</span>
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Material Design Scrim -->
        <div class="mdc-drawer-scrim" :class="{'mdc-drawer-scrim--open': drawerOpen}" @click="closeDrawer"></div>

        <!-- Material Design Top App Bar -->
        <header class="mdc-top-app-bar">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="mdc-icon-button mdc-top-app-bar__navigation-icon" @click="toggleDrawer">
                        <span class="material-icons mdc-icon-button__icon">menu</span>
                    </button>
                    <span class="mdc-top-app-bar__title">Students</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end">
                    <button class="mdc-icon-button mdc-top-app-bar__action-item" @click="toggleSearch">
                        <span class="material-icons mdc-icon-button__icon">search</span>
                    </button>
                    <button class="mdc-icon-button mdc-top-app-bar__action-item" @click="showAddStudent">
                        <span class="material-icons mdc-icon-button__icon">add</span>
                    </button>
                    <button class="mdc-icon-button mdc-top-app-bar__action-item">
                        <span class="material-icons mdc-icon-button__icon">more_vert</span>
                    </button>
                </section>
            </div>

            <!-- Material Design Search Bar -->
            <div class="search-container" :class="{'search-active': searchActive}">
                <label class="mdc-text-field mdc-text-field--filled">
                    <span class="mdc-text-field__ripple"></span>
                    <span class="mdc-floating-label">Search students...</span>
                    <input class="mdc-text-field__input" type="text" x-model="searchQuery">
                    <span class="mdc-line-ripple"></span>
                </label>
                <button class="mdc-icon-button" @click="clearSearch" x-show="searchQuery">
                    <span class="material-icons mdc-icon-button__icon">close</span>
                </button>
            </div>
        </header>

        <!-- Material Design Filter Chips -->
        <div class="filter-container">
            <div class="filter-chips">
                <div class="mdc-chip-set mdc-chip-set--filter">
                    <div class="mdc-chip" :class="{'mdc-chip--selected': filter === 'all'}" @click="setFilter('all')">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__text">All Students</span>
                    </div>
                    <div class="mdc-chip" :class="{'mdc-chip--selected': filter === 'paid'}" @click="setFilter('paid')">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__text">Fully Paid</span>
                    </div>
                    <div class="mdc-chip" :class="{'mdc-chip--selected': filter === 'pending'}" @click="setFilter('pending')">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__text">Pending Payment</span>
                    </div>
                    <div class="mdc-chip" :class="{'mdc-chip--selected': filter === 'overdue'}" @click="setFilter('overdue')">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__text">Overdue</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Students List -->
        <div class="students-container">
            <template x-for="student in filteredStudents" :key="student.id">
                <div class="student-card" @click="openDetailModal(student)">
                    <div class="student-media">
                        <div class="student-avatar" :style="'background-color: ' + student.avatarColor">
                            <span class="material-icons" x-show="!student.photo">person</span>
                            <img x-show="student.photo" :src="student.photo" :alt="student.name">
                        </div>
                        <div class="student-status" :class="student.paymentStatus">
                            <span class="material-icons" x-text="getStatusIcon(student.paymentStatus)"></span>
                        </div>
                    </div>

                    <div class="student-content">
                        <div class="student-info">
                            <h3 class="student-name" x-text="student.name"></h3>
                            <p class="student-id" x-text="'ID: ' + student.studentId"></p>
                        </div>

                        <div class="payment-info">
                            <div class="payment-row">
                                <span class="payment-label">Paid:</span>
                                <span class="payment-amount paid" x-text="'$' + student.amountPaid"></span>
                            </div>
                            <div class="payment-row">
                                <span class="payment-label">Remaining:</span>
                                <span class="payment-amount remaining" x-text="'$' + student.amountRemaining"></span>
                            </div>
                        </div>
                    </div>

                    <div class="student-actions">
                        <button class="action-btn-small" @click.stop="editStudent(student)" title="Edit">
                            <span class="material-icons">edit</span>
                        </button>
                        <!-- <button class="action-btn-small" @click.stop="deleteStudent(student)" title="Delete">
                            <span class="material-icons">delete</span>
                        </button> -->
                        <button class="action-btn-small" @click.stop="showMore(student)" title="More">
                            <span class="material-icons">more_horiz</span>
                        </button>
                    </div>
                </div>
            </template>

            <!-- Empty State -->
            <div class="empty-state" x-show="filteredStudents.length === 0">
                <div class="empty-icon">
                    <span class="material-icons">school</span>
                </div>
                <h3>No students found</h3>
                <p>Try adjusting your search or filter criteria</p>
                <button class="btn-primary" @click="showAddStudent">
                    <span class="material-icons">add</span>
                    Add Student
                </button>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="fab" @click="showAddStudent" title="Add Student">
            <span class="material-icons">add</span>
        </button>

        <!-- Bottom Sheet for Actions -->
        <div class="bottom-sheet" :class="{'active': bottomSheetActive}" @click="closeBottomSheet">
            <div class="bottom-sheet-content" @click.stop>
                <div class="bottom-sheet-header">
                    <h3 x-text="selectedStudent?.name"></h3>
                    <button class="close-btn" @click="closeBottomSheet">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="bottom-sheet-actions">
                    <button class="sheet-action" @click="viewDetails">
                        <span class="material-icons">visibility</span>
                        View Details
                    </button>
                    <button class="sheet-action" @click="editStudent(selectedStudent)">
                        <span class="material-icons">edit</span>
                        Edit Student
                    </button>
                    <button class="sheet-action" @click="recordPayment">
                        <span class="material-icons">payment</span>
                        Record Payment
                    </button>
                    <button class="sheet-action" @click="sendMessage">
                        <span class="material-icons">message</span>
                        Send Message
                    </button>
                    <button class="sheet-action danger" @click="deleteStudent(selectedStudent)">
                        <span class="material-icons">delete</span>
                        Delete Student
                    </button>
                </div>
            </div>
        </div>

        <!-- Overlay -->
        <div class="overlay" :class="{'active': bottomSheetActive}" @click="closeBottomSheet"></div>

        <!-- Student Detail Modal -->
        <div class="student-detail-modal" :class="{'modal-active': detailModalActive}">
            <div class="detail-header">
                <button class="back-btn" @click="closeDetailModal">
                    <span class="material-icons">arrow_back</span>
                </button>
                <h2 class="detail-title">Student Details</h2>
                <button class="edit-btn" @click="editStudent(selectedStudentDetail)">
                    <span class="material-icons">edit</span>
                </button>
            </div>

            <div class="detail-content" x-show="selectedStudentDetail">
                <!-- Student Profile Section -->
                <div class="profile-section">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <div class="student-avatar large" :style="'background-color: ' + selectedStudentDetail?.avatarColor">
                                <span class="material-icons" x-show="!selectedStudentDetail?.photo">person</span>
                                <img x-show="selectedStudentDetail?.photo" :src="selectedStudentDetail?.photo" :alt="selectedStudentDetail?.name">
                            </div>
                            <div class="profile-status" :class="selectedStudentDetail?.paymentStatus">
                                <span class="material-icons" x-text="getStatusIcon(selectedStudentDetail?.paymentStatus)"></span>
                            </div>
                        </div>
                        <div class="profile-info">
                            <h1 class="student-name" x-text="selectedStudentDetail?.name"></h1>
                            <p class="student-id" x-text="'ID: ' + selectedStudentDetail?.studentId"></p>
                            <div class="profile-badges">
                                <span class="badge grade-badge" x-text="'Grade ' + selectedStudentDetail?.grade"></span>
                                <span class="badge status-badge" :class="selectedStudentDetail?.paymentStatus" x-text="selectedStudentDetail?.paymentStatus"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">person</span>
                        Personal Information
                    </h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Full Name</span>
                            <span class="info-value" x-text="selectedStudentDetail?.name"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date of Birth</span>
                            <span class="info-value" x-text="selectedStudentDetail?.dateOfBirth"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Age</span>
                            <span class="info-value" x-text="selectedStudentDetail?.age + ' years'"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Gender</span>
                            <span class="info-value" x-text="selectedStudentDetail?.gender"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Email</span>
                            <span class="info-value" x-text="selectedStudentDetail?.email"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Phone</span>
                            <span class="info-value" x-text="selectedStudentDetail?.phone"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Address</span>
                            <span class="info-value" x-text="selectedStudentDetail?.address"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Guardian</span>
                            <span class="info-value" x-text="selectedStudentDetail?.guardian"></span>
                        </div>
                    </div>
                </div>

                <!-- Academic Performance -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">school</span>
                        Academic Performance
                    </h3>
                    <div class="performance-summary">
                        <div class="performance-card">
                            <div class="performance-icon">
                                <span class="material-icons">grade</span>
                            </div>
                            <div class="performance-info">
                                <h4 x-text="selectedStudentDetail?.overallGPA"></h4>
                                <p>Overall GPA</p>
                            </div>
                        </div>
                        <div class="performance-card">
                            <div class="performance-icon">
                                <span class="material-icons">trending_up</span>
                            </div>
                            <div class="performance-info">
                                <h4 x-text="selectedStudentDetail?.classRank"></h4>
                                <p>Class Rank</p>
                            </div>
                        </div>
                        <div class="performance-card">
                            <div class="performance-icon">
                                <span class="material-icons">event_available</span>
                            </div>
                            <div class="performance-info">
                                <h4 x-text="selectedStudentDetail?.attendanceRate + '%'"></h4>
                                <p>Attendance</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Term Results -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">assessment</span>
                        Term Results Summary
                    </h3>
                    <div class="terms-container">
                        <template x-for="term in selectedStudentDetail?.termResults" :key="term.term">
                            <div class="term-card">
                                <div class="term-header">
                                    <h4 x-text="term.term"></h4>
                                    <span class="term-gpa" x-text="term.gpa"></span>
                                </div>
                                <div class="subjects-list">
                                    <template x-for="subject in term.subjects" :key="subject.name">
                                        <div class="subject-item">
                                            <span class="subject-name" x-text="subject.name"></span>
                                            <span class="subject-grade" :class="getGradeClass(subject.grade)" x-text="subject.grade"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Attendance Summary -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">event_available</span>
                        Attendance Summary
                    </h3>
                    <div class="attendance-stats">
                        <div class="attendance-stat">
                            <div class="stat-circle present">
                                <span x-text="selectedStudentDetail?.attendanceStats?.present"></span>
                            </div>
                            <p>Present</p>
                        </div>
                        <div class="attendance-stat">
                            <div class="stat-circle absent">
                                <span x-text="selectedStudentDetail?.attendanceStats?.absent"></span>
                            </div>
                            <p>Absent</p>
                        </div>
                        <div class="attendance-stat">
                            <div class="stat-circle late">
                                <span x-text="selectedStudentDetail?.attendanceStats?.late"></span>
                            </div>
                            <p>Late</p>
                        </div>
                        <div class="attendance-stat">
                            <div class="stat-circle excused">
                                <span x-text="selectedStudentDetail?.attendanceStats?.excused"></span>
                            </div>
                            <p>Excused</p>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">payment</span>
                        Payment Information
                    </h3>
                    <div class="payment-summary">
                        <div class="payment-item">
                            <span class="payment-label">Total Fees</span>
                            <span class="payment-amount total" x-text="'$' + selectedStudentDetail?.totalFees"></span>
                        </div>
                        <div class="payment-item">
                            <span class="payment-label">Amount Paid</span>
                            <span class="payment-amount paid" x-text="'$' + selectedStudentDetail?.amountPaid"></span>
                        </div>
                        <div class="payment-item">
                            <span class="payment-label">Amount Remaining</span>
                            <span class="payment-amount remaining" x-text="'$' + selectedStudentDetail?.amountRemaining"></span>
                        </div>
                        <div class="payment-item">
                            <span class="payment-label">Last Payment</span>
                            <span class="payment-date" x-text="selectedStudentDetail?.lastPaymentDate"></span>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="info-section">
                    <h3 class="section-title">
                        <span class="material-icons">contact_phone</span>
                        Emergency Contacts
                    </h3>
                    <div class="contacts-list">
                        <template x-for="contact in selectedStudentDetail?.emergencyContacts" :key="contact.name">
                            <div class="contact-item">
                                <div class="contact-avatar">
                                    <span class="material-icons">person</span>
                                </div>
                                <div class="contact-info">
                                    <h4 x-text="contact.name"></h4>
                                    <p x-text="contact.relationship"></p>
                                    <p x-text="contact.phone"></p>
                                </div>
                                <button class="contact-action">
                                    <span class="material-icons">call</span>
                                </button>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Design Components JS -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Custom JS -->
    <script src="ripple.js"></script>
    <script src="list-app.js"></script>
</body>
</html>