/* Material Design Ripple Effects */

/* Base ripple container */
.ripple {
    position: relative;
    overflow: hidden;
    transform: translate3d(0, 0, 0);
}

.ripple:focus {
    outline: none;
}

/* Ripple effect animation */
.ripple::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: currentColor;
    opacity: 0;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s, opacity 0.6s;
    pointer-events: none;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
    opacity: 0.12;
    transition: 0s;
}

/* Ripple for light backgrounds */
.ripple-light::before {
    background: rgba(0, 0, 0, 0.12);
}

/* Ripple for dark backgrounds */
.ripple-dark::before {
    background: rgba(255, 255, 255, 0.12);
}

/* Ripple for primary colored backgrounds */
.ripple-primary::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Ripple for accent colored backgrounds */
.ripple-accent::before {
    background: rgba(255, 255, 255, 0.16);
}

/* Custom ripple animation using JavaScript */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.3;
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Specific ripple styles for different components */

/* Button ripples */
.btn.ripple::before,
.action-btn.ripple::before,
.action-btn-small.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

.btn-primary.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

.btn-text.ripple::before {
    background: rgba(98, 0, 238, 0.12);
}

/* Card ripples */
.student-card.ripple::before,
.grade-card.ripple::before,
.attendance-card.ripple::before,
.action-card.ripple::before {
    background: rgba(0, 0, 0, 0.08);
}

/* FAB ripples */
.fab.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Filter chip ripples */
.filter-chip.ripple::before {
    background: rgba(98, 0, 238, 0.12);
}

.filter-chip.active.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Navigation item ripples */
.nav-item.ripple::before {
    background: rgba(98, 0, 238, 0.08);
}

/* Header action ripples */
.header .action-btn.ripple::before,
.header .menu-btn.ripple::before,
.header .back-btn.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Bottom sheet action ripples */
.sheet-action.ripple::before {
    background: rgba(0, 0, 0, 0.08);
}

.sheet-action.danger.ripple::before {
    background: rgba(176, 0, 32, 0.12);
}

/* Contact action ripples */
.contact-action.ripple::before {
    background: rgba(98, 0, 238, 0.12);
}

/* Performance card ripples */
.performance-card.ripple::before,
.summary-card.ripple::before,
.stat-card.ripple::before {
    background: rgba(0, 0, 0, 0.06);
}

/* Term card ripples */
.term-card.ripple::before {
    background: rgba(0, 0, 0, 0.06);
}

/* Attendance stat ripples */
.attendance-stat.ripple::before {
    background: rgba(0, 0, 0, 0.08);
}

/* Date navigation ripples */
.date-nav-btn.ripple::before {
    background: rgba(0, 0, 0, 0.08);
}

/* Search clear button ripples */
.clear-search.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Close button ripples */
.close-btn.ripple::before,
.close-drawer.ripple::before {
    background: rgba(0, 0, 0, 0.08);
}

/* Detail header button ripples */
.detail-header .back-btn.ripple::before,
.detail-header .edit-btn.ripple::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Responsive ripple sizes */
@media (max-width: 768px) {
    .ripple:active::before {
        width: 200px;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .ripple:active::before {
        width: 150px;
        height: 150px;
    }
}

/* Accessibility - reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .ripple::before {
        transition: none;
    }
    
    .ripple:active::before {
        transition: none;
        opacity: 0.06;
    }
    
    .ripple-effect {
        animation: none;
        opacity: 0.06;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .ripple:active::before {
        opacity: 0.16;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ripple::before {
        opacity: 0.2;
    }
    
    .ripple:active::before {
        opacity: 0.3;
    }
}
