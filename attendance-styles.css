/* Attendance Page Specific Styles */

/* Date Navigation */
.date-navigation {
    background-color: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.date-nav-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.date-nav-btn:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: var(--text-primary);
}

.current-date {
    text-align: center;
    flex: 1;
}

.current-date h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    color: var(--text-primary);
}

.current-date p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Attendance Summary */
.attendance-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    padding: 16px;
    background-color: var(--background-color);
}

.summary-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-shadow: var(--elevation-1);
}

.summary-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.summary-card.present .summary-icon {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.summary-card.absent .summary-icon {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.summary-card.late .summary-icon {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.summary-card.total .summary-icon {
    background-color: rgba(98, 0, 238, 0.1);
    color: var(--primary-color);
}

.summary-info h4 {
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.summary-info p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* Attendance Container */
.attendance-container {
    padding: 8px 16px 80px;
}

/* Attendance Card */
.attendance-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: var(--elevation-1);
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.attendance-card:hover {
    box-shadow: var(--elevation-2);
    transform: translateY(-1px);
}

.attendance-card:active {
    transform: translateY(0);
    box-shadow: var(--elevation-1);
}

/* Attendance Media */
.attendance-media {
    position: relative;
    flex-shrink: 0;
}

.attendance-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--surface-color);
    font-size: 12px;
}

.attendance-status.present {
    background-color: #4caf50;
    color: var(--text-light);
}

.attendance-status.absent {
    background-color: #f44336;
    color: var(--text-light);
}

.attendance-status.late {
    background-color: #ff9800;
    color: var(--text-light);
}

.attendance-status.excused {
    background-color: #2196f3;
    color: var(--text-light);
}

/* Attendance Content */
.attendance-content {
    flex: 1;
    min-width: 0;
}

.attendance-info {
    margin-bottom: 8px;
}

.attendance-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.attendance-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attendance-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.attendance-value {
    font-size: 14px;
    font-weight: 500;
}

.attendance-value.present {
    color: #4caf50;
}

.attendance-value.absent {
    color: #f44336;
}

.attendance-value.late {
    color: #ff9800;
}

.attendance-value.excused {
    color: #2196f3;
}

.attendance-time {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Attendance Actions */
.attendance-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
}

.action-btn-small:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn-small:disabled:hover {
    background-color: transparent;
    color: var(--text-secondary);
}

/* Weekly View */
.weekly-view {
    background-color: var(--surface-color);
    margin: 16px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--elevation-1);
}

.weekly-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: between;
    align-items: center;
}

.weekly-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    color: var(--text-primary);
}

.weekly-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.day-header {
    padding: 12px 8px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
}

.day-cell {
    padding: 8px;
    text-align: center;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.day-number {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.day-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.day-status.present {
    background-color: #4caf50;
}

.day-status.absent {
    background-color: #f44336;
}

.day-status.late {
    background-color: #ff9800;
}

.day-status.weekend {
    background-color: var(--border-color);
}

/* Attendance Percentage */
.attendance-percentage {
    background-color: var(--surface-color);
    margin: 16px;
    padding: 16px;
    border-radius: 12px;
    box-shadow: var(--elevation-1);
    text-align: center;
}

.percentage-title {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0 0 8px;
}

.percentage-value {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px;
    color: var(--primary-color);
}

.percentage-bar {
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.percentage-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.percentage-details {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Responsive Design for Attendance */
@media (max-width: 480px) {
    .attendance-summary {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        padding: 12px;
    }
    
    .summary-card {
        padding: 8px;
    }
    
    .summary-icon {
        width: 28px;
        height: 28px;
        margin-bottom: 6px;
    }
    
    .summary-info h4 {
        font-size: 18px;
    }
    
    .attendance-card {
        padding: 12px;
        gap: 12px;
    }
    
    .attendance-actions {
        flex-direction: row;
        gap: 2px;
    }
    
    .action-btn-small {
        width: 28px;
        height: 28px;
        font-size: 18px;
    }
    
    .date-navigation {
        padding: 12px;
    }
    
    .current-date h3 {
        font-size: 16px;
    }
    
    .weekly-view {
        margin: 12px;
    }
    
    .attendance-percentage {
        margin: 12px;
        padding: 12px;
    }
    
    .percentage-value {
        font-size: 28px;
    }
}

@media (max-width: 360px) {
    .attendance-summary {
        grid-template-columns: 1fr;
    }
    
    .summary-card {
        flex-direction: row;
        text-align: left;
        gap: 12px;
    }
    
    .summary-icon {
        margin-bottom: 0;
    }
    
    .attendance-container {
        padding: 8px 12px 80px;
    }
    
    .date-navigation {
        padding: 8px;
    }
}
