<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration Form</title>
    <!-- Bootstrap 4.5 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        .form-header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            border-radius: 5px 5px 0 0;
            margin-bottom: 0;
        }
        
        .registration-form {
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .form-body {
            padding: 20px;
        }
        
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
        
        .custom-file-label::after {
            content: "Browse";
        }
        
        .action-buttons {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            border-radius: 0 0 5px 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-5 mb-5">
        <h2 class="text-center mb-4">School Management System</h2>
        
        <div class="registration-form">
            <h4 class="form-header">
                <i class="fas fa-user-graduate mr-2"></i>Student Registration Form
            </h4>
            
            <form class="form-body">
                <!-- Personal Information Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label for="firstName" class="required-field">First Name</label>
                                <input type="text" class="form-control" id="firstName" required>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="middleName">Middle Name</label>
                                <input type="text" class="form-control" id="middleName">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="lastName" class="required-field">Last Name</label>
                                <input type="text" class="form-control" id="lastName" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="dob" class="required-field">Date of Birth</label>
                                <input type="date" class="form-control" id="dob" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="gender" class="required-field">Gender</label>
                                <select class="form-control" id="gender" required>
                                    <option value="" selected disabled>Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="email" class="required-field">Email Address</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="phone" class="required-field">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="photo">Student Photo</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="photo">
                                <label class="custom-file-label" for="photo">Choose file</label>
                            </div>
                            <small class="form-text text-muted">Upload a recent passport-sized photo (JPEG, PNG; max 1MB)</small>
                        </div>
                    </div>
                </div>
                
                <!-- Academic Information Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Academic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="grade" class="required-field">Grade/Class</label>
                                <select class="form-control" id="grade" required>
                                    <option value="" selected disabled>Select Grade</option>
                                    <option value="1">Grade 1</option>
                                    <option value="2">Grade 2</option>
                                    <option value="3">Grade 3</option>
                                    <option value="4">Grade 4</option>
                                    <option value="5">Grade 5</option>
                                    <option value="6">Grade 6</option>
                                    <option value="7">Grade 7</option>
                                    <option value="8">Grade 8</option>
                                    <option value="9">Grade 9</option>
                                    <option value="10">Grade 10</option>
                                    <option value="11">Grade 11</option>
                                    <option value="12">Grade 12</option>
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="section">Section</label>
                                <select class="form-control" id="section">
                                    <option value="" selected disabled>Select Section</option>
                                    <option value="A">A</option>
                                    <option value="B">B</option>
                                    <option value="C">C</option>
                                    <option value="D">D</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="previousSchool">Previous School (if any)</label>
                                <input type="text" class="form-control" id="previousSchool">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="academicYear" class="required-field">Academic Year</label>
                                <select class="form-control" id="academicYear" required>
                                    <option value="" selected disabled>Select Academic Year</option>
                                    <option value="2024-2025">2024-2025</option>
                                    <option value="2025-2026">2025-2026</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="address" class="required-field">Address</label>
                            <textarea class="form-control" id="address" rows="3" required></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="city" class="required-field">City</label>
                                <input type="text" class="form-control" id="city" required>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="state" class="required-field">State/Province</label>
                                <input type="text" class="form-control" id="state" required>
                            </div>
                            <div class="form-group col-md-2">
                                <label for="zipCode" class="required-field">Zip Code</label>
                                <input type="text" class="form-control" id="zipCode" required>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Parent/Guardian Information -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Parent/Guardian Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="parentName" class="required-field">Parent/Guardian Name</label>
                                <input type="text" class="form-control" id="parentName" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="relationship" class="required-field">Relationship</label>
                                <select class="form-control" id="relationship" required>
                                    <option value="" selected disabled>Select Relationship</option>
                                    <option value="father">Father</option>
                                    <option value="mother">Mother</option>
                                    <option value="guardian">Guardian</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="parentEmail" class="required-field">Email Address</label>
                                <input type="email" class="form-control" id="parentEmail" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="parentPhone" class="required-field">Phone Number</label>
                                <input type="tel" class="form-control" id="parentPhone" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="occupation">Occupation</label>
                            <input type="text" class="form-control" id="occupation">
                        </div>
                    </div>
                </div>
                
                <!-- Emergency Contact -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Emergency Contact</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="emergencyName" class="required-field">Contact Name</label>
                                <input type="text" class="form-control" id="emergencyName" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="emergencyRelationship" class="required-field">Relationship</label>
                                <input type="text" class="form-control" id="emergencyRelationship" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="emergencyPhone" class="required-field">Phone Number</label>
                                <input type="tel" class="form-control" id="emergencyPhone" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="emergencyEmail">Email Address</label>
                                <input type="email" class="form-control" id="emergencyEmail">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Declaration and Agreement -->
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="declaration" required>
                        <label class="custom-control-label" for="declaration">
                            I declare that the information provided is true and accurate to the best of my knowledge.
                        </label>
                    </div>
                </div>
                
                <div class="action-buttons text-right">
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo mr-1"></i>Reset
                    </button>
                    <button type="submit" class="btn btn-primary ml-2">
                        <i class="fas fa-save mr-1"></i>Submit Registration
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Required JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Display file name when a file is selected
            $(".custom-file-input").on("change", function() {
                var fileName = $(this).val().split("\\").pop();
                $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
            });
            
            // Form validation example
            $("form").on("submit", function(e) {
                e.preventDefault();
                
                // Add your form validation logic here
                
                // If validation passes, show success message
                alert("Registration form submitted successfully!");
                
                // You would typically send the form data to the server here
                // For demo purposes, we're just showing an alert
            });
        });
    </script>
</body>
</html>
