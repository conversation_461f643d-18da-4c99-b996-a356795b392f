<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>School Management System</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    
    body {
      background-color: #f3f4f6;
      padding: 20px;
    }
    
    .container {
      max-width: 600px;
      margin: 40px auto;
    }
    
    .header {
      margin-bottom: 24px;
    }
    
    .header h1 {
      font-size: 24px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .header p {
      color: #6b7280;
    }
    
    .progress-container {
      margin-bottom: 32px;
    }
    
    .progress-labels {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .progress-label {
      font-size: 14px;
      font-weight: 500;
      color: #9ca3af;
      transition: color 0.3s ease;
    }
    
    .progress-label.active {
      color: #2563eb;
    }
    
    .progress-bar {
      width: 100%;
      height: 10px;
      background-color: #e5e7eb;
      border-radius: 9999px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background-color: #2563eb;
      width: 0%;
      transition: width 0.3s ease;
      border-radius: 9999px;
    }
    
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 24px;
      margin-bottom: 16px;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .card-header h2 {
      font-size: 18px;
      font-weight: bold;
    }
    
    .context-info {
      color: #6b7280;
    }
    
    .selection-button {
      width: 100%;
      text-align: left;
      padding: 12px 16px;
      margin-bottom: 12px;
      background-color: white;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 16px;
    }
    
    .selection-button:hover {
      border-color: #2563eb;
      background-color: #eff6ff;
    }
    
    .back-button {
      background: none;
      border: none;
      color: #2563eb;
      cursor: pointer;
      margin-top: 16px;
      font-size: 14px;
      display: flex;
      align-items: center;
      padding: 4px 0;
    }
    
    .back-button:hover {
      color: #1d4ed8;
      text-decoration: underline;
    }
    
    .back-button::before {
      content: "←";
      margin-right: 4px;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 16px;
    }
    
    th {
      text-align: left;
      padding: 12px 16px;
      background-color: #f9fafb;
      color: #6b7280;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border-bottom: 1px solid #e5e7eb;
    }
    
    td {
      padding: 12px 16px;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .student-name {
      font-weight: 500;
      color: #1f2937;
    }
    
    .student-grade {
      color: #6b7280;
    }
    
    .step {
      display: none;
    }
    
    .step.active {
      display: block;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>School Management System</h1>
      <p>Access student grades by term and level</p>
    </div>
    
    <div class="progress-container">
      <div class="progress-labels">
        <div id="step1-label" class="progress-label active">Select Term</div>
        <div id="step2-label" class="progress-label">Select Level</div>
        <div id="step3-label" class="progress-label">View Grades</div>
      </div>
      <div class="progress-bar">
        <div id="progress-fill" class="progress-fill"></div>
      </div>
    </div>
    
    <!-- Step 1: Term Selection -->
    <div id="step1" class="step active">
      <div class="card">
        <div class="card-header">
          <h2>Select a School Term</h2>
        </div>
        <div id="term-options" class="options-container">
          <!-- Term options will be inserted here by JavaScript -->
        </div>
      </div>
    </div>
    
    <!-- Step 2: Level Selection -->
    <div id="step2" class="step">
      <div class="card">
        <div class="card-header">
          <h2>Select a School Level</h2>
          <div id="selected-term" class="context-info"></div>
        </div>
        <div id="level-options" class="options-container">
          <!-- Level options will be inserted here by JavaScript -->
        </div>
        <button id="back-to-term" class="back-button">Go back to term selection</button>
      </div>
    </div>
    
    <!-- Step 3: Grades Display -->
    <div id="step3" class="step">
      <div class="card">
        <div class="card-header">
          <h2>Student Grades</h2>
          <div id="selected-context" class="context-info"></div>
        </div>
        <table>
          <thead>
            <tr>
              <th>Student Name</th>
              <th>Grade</th>
            </tr>
          </thead>
          <tbody id="grades-table-body">
            <!-- Student data will be inserted here by JavaScript -->
          </tbody>
        </table>
        <button id="back-to-level" class="back-button">Go back to level selection</button>
      </div>
    </div>
  </div>

  <script>
    // Mock data
    const schoolData = {
      terms: ["Fall 2024", "Spring 2025", "Summer 2025"],
      levels: ["Elementary", "Middle School", "High School"],
      students: {
        "Fall 2024": {
          "Elementary": [
            { name: "Alice", grade: "A" },
            { name: "Bob", grade: "B+" },
            { name: "Charlie", grade: "A-" }
          ],
          "Middle School": [
            { name: "David", grade: "B" },
            { name: "Emma", grade: "A" },
            { name: "Frank", grade: "C+" }
          ],
          "High School": [
            { name: "Grace", grade: "A-" },
            { name: "Henry", grade: "B-" },
            { name: "Ivy", grade: "A+" }
          ]
        },
        "Spring 2025": {
          "Elementary": [
            { name: "Alice", grade: "A+" },
            { name: "Bob", grade: "B" },
            { name: "Charlie", grade: "A" }
          ],
          "Middle School": [
            { name: "David", grade: "B+" },
            { name: "Emma", grade: "A-" },
            { name: "Frank", grade: "C" }
          ],
          "High School": [
            { name: "Grace", grade: "B+" },
            { name: "Henry", grade: "B" },
            { name: "Ivy", grade: "A" }
          ]
        },
        "Summer 2025": {
          "Elementary": [
            { name: "Alice", grade: "A-" },
            { name: "Bob", grade: "B-" },
            { name: "Charlie", grade: "A" }
          ],
          "Middle School": [
            { name: "David", grade: "B" },
            { name: "Emma", grade: "A" },
            { name: "Frank", grade: "C-" }
          ],
          "High School": [
            { name: "Grace", grade: "B" },
            { name: "Henry", grade: "B+" },
            { name: "Ivy", grade: "A-" }
          ]
        }
      }
    };

    // State variables
    let currentStep = 1;
    let selectedTerm = null;
    let selectedLevel = null;

    // DOM Elements
    const step1Element = document.getElementById('step1');
    const step2Element = document.getElementById('step2');
    const step3Element = document.getElementById('step3');
    const step1Label = document.getElementById('step1-label');
    const step2Label = document.getElementById('step2-label');
    const step3Label = document.getElementById('step3-label');
    const progressFill = document.getElementById('progress-fill');
    const termOptionsContainer = document.getElementById('term-options');
    const levelOptionsContainer = document.getElementById('level-options');
    const gradesTableBody = document.getElementById('grades-table-body');
    const selectedTermElement = document.getElementById('selected-term');
    const selectedContextElement = document.getElementById('selected-context');
    const backToTermButton = document.getElementById('back-to-term');
    const backToLevelButton = document.getElementById('back-to-level');

    // Initialize the wizard
    function init() {
      renderTermOptions();
      setupBackButtons();
      updateUI();
    }

    // Render term selection options
    function renderTermOptions() {
      termOptionsContainer.innerHTML = '';
      schoolData.terms.forEach(term => {
        const button = document.createElement('button');
        button.className = 'selection-button';
        button.textContent = term;
        button.addEventListener('click', () => {
          selectedTerm = term;
          goToStep(2);
        });
        termOptionsContainer.appendChild(button);
      });
    }

    // Render level selection options
    function renderLevelOptions() {
      levelOptionsContainer.innerHTML = '';
      schoolData.levels.forEach(level => {
        const button = document.createElement('button');
        button.className = 'selection-button';
        button.textContent = level;
        button.addEventListener('click', () => {
          selectedLevel = level;
          goToStep(3);
        });
        levelOptionsContainer.appendChild(button);
      });
    }

    // Render grades table
    function renderGradesTable() {
      gradesTableBody.innerHTML = '';
      const students = schoolData.students[selectedTerm][selectedLevel];
      
      students.forEach(student => {
        const row = document.createElement('tr');
        
        const nameCell = document.createElement('td');
        nameCell.className = 'student-name';
        nameCell.textContent = student.name;
        
        const gradeCell = document.createElement('td');
        gradeCell.className = 'student-grade';
        gradeCell.textContent = student.grade;
        
        row.appendChild(nameCell);
        row.appendChild(gradeCell);
        gradesTableBody.appendChild(row);
      });
    }

    // Set up back button event listeners
    function setupBackButtons() {
      backToTermButton.addEventListener('click', () => {
        goToStep(1);
        selectedTerm = null;
      });
      
      backToLevelButton.addEventListener('click', () => {
        goToStep(2);
        selectedLevel = null;
      });
    }

    // Navigate to a specific step
    function goToStep(step) {
      currentStep = step;
      updateUI();
    }

    // Update the UI based on the current state
    function updateUI() {
      // Hide all steps
      step1Element.classList.remove('active');
      step2Element.classList.remove('active');
      step3Element.classList.remove('active');
      
      // Reset all labels
      step1Label.classList.remove('active');
      step2Label.classList.remove('active');
      step3Label.classList.remove('active');
      
      // Show the current step
      if (currentStep === 1) {
        step1Element.classList.add('active');
        step1Label.classList.add('active');
        progressFill.style.width = '0%';
      } else if (currentStep === 2) {
        step2Element.classList.add('active');
        step1Label.classList.add('active');
        step2Label.classList.add('active');
        progressFill.style.width = '50%';
        selectedTermElement.textContent = selectedTerm;
        renderLevelOptions();
      } else if (currentStep === 3) {
        step3Element.classList.add('active');
        step1Label.classList.add('active');
        step2Label.classList.add('active');
        step3Label.classList.add('active');
        progressFill.style.width = '100%';
        selectedContextElement.textContent = `${selectedTerm} • ${selectedLevel}`;
        renderGradesTable();
      }
    }

    // Start the application
    init();
  </script>
</body>
</html>