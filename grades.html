<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grades - Material Design</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="list-styles.css">
    <link rel="stylesheet" href="grades-styles.css">
</head>
<body>
    <div x-data="gradesList()" class="app-container">
        <!-- Navigation Drawer -->
        <nav class="nav-drawer" :class="{'drawer-open': drawerOpen}">
            <div class="drawer-header">
                <div class="app-info">
                    <div class="app-icon">
                        <span class="material-icons">school</span>
                    </div>
                    <div class="app-details">
                        <h3>EduManager</h3>
                        <p>Student Management System</p>
                    </div>
                </div>
                <button class="close-drawer" @click="closeDrawer">
                    <span class="material-icons">close</span>
                </button>
            </div>
            
            <div class="drawer-content">
                <div class="nav-section">
                    <h4 class="nav-section-title">Main</h4>
                    <a href="dashboard.html" class="nav-item">
                        <span class="material-icons">dashboard</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="list.html" class="nav-item">
                        <span class="material-icons">people</span>
                        <span class="nav-text">Students</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Academic</h4>
                    <a href="grades.html" class="nav-item active">
                        <span class="material-icons">grade</span>
                        <span class="nav-text">Grades</span>
                    </a>
                    <a href="attendance.html" class="nav-item">
                        <span class="material-icons">event_available</span>
                        <span class="nav-text">Attendance</span>
                    </a>
                    <a href="courses.html" class="nav-item">
                        <span class="material-icons">book</span>
                        <span class="nav-text">Courses</span>
                    </a>
                    <a href="schedule.html" class="nav-item">
                        <span class="material-icons">schedule</span>
                        <span class="nav-text">Schedule</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Financial</h4>
                    <a href="payments.html" class="nav-item">
                        <span class="material-icons">payment</span>
                        <span class="nav-text">Payments</span>
                    </a>
                    <a href="fees.html" class="nav-item">
                        <span class="material-icons">account_balance</span>
                        <span class="nav-text">Fee Structure</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Communication</h4>
                    <a href="messages.html" class="nav-item">
                        <span class="material-icons">message</span>
                        <span class="nav-text">Messages</span>
                    </a>
                    <a href="notifications.html" class="nav-item">
                        <span class="material-icons">notifications</span>
                        <span class="nav-text">Notifications</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <h4 class="nav-section-title">Reports</h4>
                    <a href="analytics.html" class="nav-item">
                        <span class="material-icons">analytics</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                    <a href="reports.html" class="nav-item">
                        <span class="material-icons">assessment</span>
                        <span class="nav-text">Reports</span>
                    </a>
                </div>
            </div>
            
            <div class="drawer-footer">
                <a href="settings.html" class="nav-item">
                    <span class="material-icons">settings</span>
                    <span class="nav-text">Settings</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <span class="material-icons">account_circle</span>
                    <span class="nav-text">Profile</span>
                </a>
            </div>
        </nav>
        
        <!-- Drawer Overlay -->
        <div class="drawer-overlay" :class="{'overlay-active': drawerOpen}" @click="closeDrawer"></div>

        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <button class="menu-btn" @click="toggleDrawer">
                    <span class="material-icons">menu</span>
                </button>
                <h1 class="page-title">Grades</h1>
                <div class="header-actions">
                    <button class="action-btn" @click="toggleSearch">
                        <span class="material-icons">search</span>
                    </button>
                    <button class="action-btn" @click="showAddGrade">
                        <span class="material-icons">add</span>
                    </button>
                    <button class="action-btn">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-container" :class="{'search-active': searchActive}">
                <div class="search-box">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" placeholder="Search grades..." class="search-input" x-model="searchQuery">
                    <button class="clear-search" @click="clearSearch" x-show="searchQuery">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Filter Chips -->
        <div class="filter-container">
            <div class="filter-chips">
                <button class="filter-chip" :class="{'active': filter === 'all'}" @click="setFilter('all')">
                    All Grades
                </button>
                <button class="filter-chip" :class="{'active': filter === 'excellent'}" @click="setFilter('excellent')">
                    A Grade
                </button>
                <button class="filter-chip" :class="{'active': filter === 'good'}" @click="setFilter('good')">
                    B Grade
                </button>
                <button class="filter-chip" :class="{'active': filter === 'average'}" @click="setFilter('average')">
                    C Grade
                </button>
                <button class="filter-chip" :class="{'active': filter === 'poor'}" @click="setFilter('poor')">
                    Below C
                </button>
            </div>
        </div>

        <!-- Grades List -->
        <div class="grades-container">
            <template x-for="grade in filteredGrades" :key="grade.id">
                <div class="grade-card" @click="selectGrade(grade)">
                    <div class="grade-media">
                        <div class="student-avatar" :style="'background-color: ' + grade.avatarColor">
                            <span class="material-icons">person</span>
                        </div>
                        <div class="grade-badge" :class="getGradeClass(grade.grade)">
                            <span x-text="grade.grade"></span>
                        </div>
                    </div>
                    
                    <div class="grade-content">
                        <div class="grade-info">
                            <h3 class="student-name" x-text="grade.studentName"></h3>
                            <p class="course-name" x-text="grade.courseName"></p>
                            <p class="assignment-name" x-text="grade.assignmentName"></p>
                        </div>
                        
                        <div class="grade-details">
                            <div class="grade-row">
                                <span class="grade-label">Score:</span>
                                <span class="grade-score" x-text="grade.score + '/' + grade.maxScore"></span>
                            </div>
                            <div class="grade-row">
                                <span class="grade-label">Date:</span>
                                <span class="grade-date" x-text="grade.date"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grade-actions">
                        <button class="action-btn-small" @click.stop="editGrade(grade)" title="Edit">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="action-btn-small" @click.stop="deleteGrade(grade)" title="Delete">
                            <span class="material-icons">delete</span>
                        </button>
                        <button class="action-btn-small" @click.stop="showMore(grade)" title="More">
                            <span class="material-icons">more_horiz</span>
                        </button>
                    </div>
                </div>
            </template>
            
            <!-- Empty State -->
            <div class="empty-state" x-show="filteredGrades.length === 0">
                <div class="empty-icon">
                    <span class="material-icons">grade</span>
                </div>
                <h3>No grades found</h3>
                <p>Try adjusting your search or filter criteria</p>
                <button class="btn-primary" @click="showAddGrade">
                    <span class="material-icons">add</span>
                    Add Grade
                </button>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="fab" @click="showAddGrade" title="Add Grade">
            <span class="material-icons">add</span>
        </button>

        <!-- Bottom Sheet for Actions -->
        <div class="bottom-sheet" :class="{'active': bottomSheetActive}" @click="closeBottomSheet">
            <div class="bottom-sheet-content" @click.stop>
                <div class="bottom-sheet-header">
                    <h3 x-text="selectedGrade?.studentName + ' - ' + selectedGrade?.courseName"></h3>
                    <button class="close-btn" @click="closeBottomSheet">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="bottom-sheet-actions">
                    <button class="sheet-action" @click="viewDetails">
                        <span class="material-icons">visibility</span>
                        View Details
                    </button>
                    <button class="sheet-action" @click="editGrade(selectedGrade)">
                        <span class="material-icons">edit</span>
                        Edit Grade
                    </button>
                    <button class="sheet-action" @click="sendFeedback">
                        <span class="material-icons">feedback</span>
                        Send Feedback
                    </button>
                    <button class="sheet-action" @click="exportGrade">
                        <span class="material-icons">download</span>
                        Export Grade
                    </button>
                    <button class="sheet-action danger" @click="deleteGrade(selectedGrade)">
                        <span class="material-icons">delete</span>
                        Delete Grade
                    </button>
                </div>
            </div>
        </div>

        <!-- Overlay -->
        <div class="overlay" :class="{'active': bottomSheetActive}" @click="closeBottomSheet"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Custom JS -->
    <script src="grades-app.js"></script>
</body>
</html>
