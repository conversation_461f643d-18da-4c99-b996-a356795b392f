// Alpine.js Grades List Component
function gradesList() {
    return {
        drawerOpen: false,
        searchActive: false,
        searchQuery: '',
        filter: 'all',
        bottomSheetActive: false,
        selectedGrade: null,
        
        // Sample grades data
        grades: [
            {
                id: 1,
                studentName: '<PERSON>',
                studentId: 'STU001',
                courseName: 'Mathematics',
                assignmentName: 'Algebra Test 1',
                grade: 'A',
                score: 95,
                maxScore: 100,
                date: '2024-01-15',
                avatarColor: '#e91e63'
            },
            {
                id: 2,
                studentName: '<PERSON>',
                studentId: 'STU002',
                courseName: 'Physics',
                assignmentName: 'Mechanics Quiz',
                grade: 'B+',
                score: 87,
                maxScore: 100,
                date: '2024-01-14',
                avatarColor: '#2196f3'
            },
            {
                id: 3,
                studentName: '<PERSON>',
                studentId: 'STU003',
                courseName: 'Chemistry',
                assignmentName: 'Organic Chemistry Lab',
                grade: 'A-',
                score: 92,
                maxScore: 100,
                date: '2024-01-13',
                avatarColor: '#4caf50'
            },
            {
                id: 4,
                studentName: '<PERSON>',
                studentId: 'STU004',
                courseName: 'English',
                assignmentName: 'Essay Assignment',
                grade: 'B',
                score: 83,
                maxScore: 100,
                date: '2024-01-12',
                avatarColor: '#ff9800'
            },
            {
                id: 5,
                studentName: 'Lisa Thompson',
                studentId: 'STU005',
                courseName: 'History',
                assignmentName: 'World War II Project',
                grade: 'A+',
                score: 98,
                maxScore: 100,
                date: '2024-01-11',
                avatarColor: '#9c27b0'
            },
            {
                id: 6,
                studentName: 'James Anderson',
                studentId: 'STU006',
                courseName: 'Biology',
                assignmentName: 'Cell Structure Test',
                grade: 'C+',
                score: 78,
                maxScore: 100,
                date: '2024-01-10',
                avatarColor: '#f44336'
            },
            {
                id: 7,
                studentName: 'Maria Garcia',
                studentId: 'STU007',
                courseName: 'Computer Science',
                assignmentName: 'Programming Assignment',
                grade: 'A',
                score: 94,
                maxScore: 100,
                date: '2024-01-09',
                avatarColor: '#00bcd4'
            },
            {
                id: 8,
                studentName: 'Robert Taylor',
                studentId: 'STU008',
                courseName: 'Art',
                assignmentName: 'Portfolio Review',
                grade: 'B-',
                score: 81,
                maxScore: 100,
                date: '2024-01-08',
                avatarColor: '#795548'
            }
        ],
        
        // Computed property for filtered grades
        get filteredGrades() {
            let filtered = this.grades;
            
            // Apply search filter
            if (this.searchQuery.trim()) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(grade => 
                    grade.studentName.toLowerCase().includes(query) ||
                    grade.courseName.toLowerCase().includes(query) ||
                    grade.assignmentName.toLowerCase().includes(query) ||
                    grade.grade.toLowerCase().includes(query)
                );
            }
            
            // Apply grade filter
            if (this.filter !== 'all') {
                filtered = filtered.filter(grade => {
                    const gradeValue = grade.grade.charAt(0);
                    switch (this.filter) {
                        case 'excellent':
                            return gradeValue === 'A';
                        case 'good':
                            return gradeValue === 'B';
                        case 'average':
                            return gradeValue === 'C';
                        case 'poor':
                            return gradeValue === 'D' || gradeValue === 'F';
                        default:
                            return true;
                    }
                });
            }
            
            return filtered;
        },
        
        // Toggle drawer
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },
        
        // Close drawer
        closeDrawer() {
            this.drawerOpen = false;
        },
        
        // Toggle search visibility
        toggleSearch() {
            this.searchActive = !this.searchActive;
            if (!this.searchActive) {
                this.searchQuery = '';
            }
        },
        
        // Clear search
        clearSearch() {
            this.searchQuery = '';
        },
        
        // Set filter
        setFilter(filter) {
            this.filter = filter;
        },
        
        // Get grade class for styling
        getGradeClass(grade) {
            const gradeValue = grade.charAt(0).toLowerCase();
            return `grade-${gradeValue}`;
        },
        
        // Select grade and show bottom sheet
        selectGrade(grade) {
            this.selectedGrade = grade;
            this.bottomSheetActive = true;
        },
        
        // Close bottom sheet
        closeBottomSheet() {
            this.bottomSheetActive = false;
            setTimeout(() => {
                this.selectedGrade = null;
            }, 300);
        },
        
        // Edit grade
        editGrade(grade) {
            this.closeBottomSheet();
            alert(`Edit grade: ${grade.studentName} - ${grade.courseName}`);
            // Here you would typically navigate to an edit form or open a modal
        },
        
        // Delete grade
        deleteGrade(grade) {
            if (confirm(`Are you sure you want to delete this grade for ${grade.studentName}?`)) {
                const index = this.grades.findIndex(g => g.id === grade.id);
                if (index > -1) {
                    this.grades.splice(index, 1);
                }
                this.closeBottomSheet();
            }
        },
        
        // Show more options
        showMore(grade) {
            this.selectGrade(grade);
        },
        
        // Show add grade
        showAddGrade() {
            alert('Add new grade');
            // Here you would typically navigate to an add form or open a modal
        },
        
        // View details
        viewDetails() {
            this.closeBottomSheet();
            alert(`View details for: ${this.selectedGrade.studentName} - ${this.selectedGrade.courseName}`);
            // Here you would typically navigate to a details page
        },
        
        // Send feedback
        sendFeedback() {
            this.closeBottomSheet();
            alert(`Send feedback for: ${this.selectedGrade.studentName}`);
            // Here you would typically open a feedback form
        },
        
        // Export grade
        exportGrade() {
            this.closeBottomSheet();
            alert(`Export grade for: ${this.selectedGrade.studentName}`);
            // Here you would typically trigger a download
        },
        
        // Initialize component
        init() {
            console.log('Grades list initialized');
        }
    };
}
